# ساعة المسجد - مواقيت الصلاة

تطبيق ويب شامل لعرض مواقيت الصلاة والساعة للمساجد مع إمكانيات متقدمة للتخصيص والإعدادات.

## 🌟 المميزات

### ⏰ عرض الوقت
- **ساعة رقمية** مع عرض الثواني والميلي ثانية
- **ساعة تناظرية** مع أرقام من 1-12
- **تاريخ هجري وميلادي** مع اسم اليوم
- **دعم المناطق الزمنية** لجميع الدول العربية

### 🕌 مواقيت الصلاة
- **حساب دقيق** لمواقيت الصلاة حسب الموقع الجغرافي
- **دعم متعدد المدن** (الأردن، السعودية، الإمارات، مصر، وغيرها)
- **طرق حساب متنوعة** (رابطة العالم الإسلامي، أم القرى، مصر، وغيرها)
- **تعديل يدوي** للمواقيت
- **نظام فروقات يدوية** للتعديل الدقيق
- **عد تنازلي** للصلاة القادمة

### 🔊 الأذان والإقامة
- **10 أصوات مختلفة للأذان**
- **5 أصوات مختلفة للإقامة**
- **تحكم في مستوى الصوت**
- **تعتيم الشاشة** أثناء الأذان والإقامة
- **مدة قابلة للتخصيص** لكل صلاة

### 🎨 التخصيص والتصميم
- **40+ خلفية مختلفة**
- **ألوان قابلة للتخصيص** لجميع العناصر
- **أحجام خط متغيرة**
- **وضع ملء الشاشة**
- **تصميم متجاوب** لجميع الشاشات

### 📱 دعم الهواتف المحمولة
- **تطبيق PWA** قابل للتثبيت
- **وضع أفقي ثابت** (لا يتغير عند الدوران)
- **منع التكبير والتصغير**
- **تحسين للشاشات اللمسية**

## 🚀 التثبيت والاستخدام

### متطلبات النظام
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- اتصال بالإنترنت (للتحديث الأولي فقط)

### التشغيل المحلي
1. قم بتحميل المشروع
2. افتح ملف `index.html` في المتصفح
3. أو استخدم خادم محلي:
```bash
# باستخدام Python
python -m http.server 8000

# باستخدام Node.js
npx serve .

# باستخدام PHP
php -S localhost:8000
```

### التثبيت كتطبيق PWA
1. افتح الموقع في المتصفح
2. اضغط على "إضافة إلى الشاشة الرئيسية"
3. سيتم تثبيت التطبيق كتطبيق أصلي

## 📋 دليل الاستخدام

### الإعدادات الأساسية
1. **اختيار المدينة**: من قائمة الإعدادات
2. **طريقة الحساب**: اختر الطريقة المناسبة لمنطقتك
3. **المذهب**: حنفي أو شافعي (يؤثر على وقت العصر)

### تخصيص المواقيت
1. **التعديل اليدوي**: إدخال أوقات محددة
2. **الفروقات اليدوية**: إضافة/طرح دقائق من الأوقات المحسوبة
3. **مدة الإقامة**: تحديد المدة لكل صلاة

### تخصيص المظهر
1. **الخلفية**: اختر من 40+ خلفية
2. **الألوان**: تخصيص ألوان جميع العناصر
3. **الخطوط**: تغيير أحجام النصوص
4. **اسم المسجد**: إضافة وتخصيص اسم المسجد

## 🛠️ التطوير

### هيكل المشروع
```
project/
├── index.html              # الملف الرئيسي
├── manifest.json           # إعدادات PWA
├── sw.js                   # Service Worker
├── backgrounds/            # ملفات الخلفيات
├── sounds/                 # ملفات الأذان والإقامة
├── icons/                  # أيقونات التطبيق
├── node_modules/           # المكتبات المطلوبة
└── js/                     # ملفات JavaScript
```

### المكتبات المستخدمة
- **Moment.js**: للتعامل مع التواريخ
- **Moment Hijri**: للتاريخ الهجري
- **PrayTimes.js**: لحساب مواقيت الصلاة

### إضافة مدن جديدة
```javascript
// في ملف index.html
const cities = {
    'المدينة الجديدة': {
        timezone: 'المنطقة/الزمنية',
        latitude: خط_العرض,
        longitude: خط_الطول
    }
};
```

## 📱 تحويل إلى APK

### باستخدام PWA Builder
1. اذهب إلى [PWABuilder.com](https://www.pwabuilder.com/)
2. أدخل رابط الموقع
3. اختر "Android Package"
4. قم بتحميل ملف APK

### باستخدام Capacitor
```bash
npm install @capacitor/core @capacitor/cli
npx cap init
npx cap add android
npx cap run android
```

## 🌐 النشر على GitHub

### إنشاء Repository
```bash
git init
git add .
git commit -m "Initial commit"
git branch -M main
git remote add origin https://github.com/username/mosque-clock.git
git push -u origin main
```

### تفعيل GitHub Pages
1. اذهب إلى Settings > Pages
2. اختر Source: Deploy from a branch
3. اختر Branch: main
4. اضغط Save

## 🔧 الإعدادات المتقدمة

### تخصيص أصوات الأذان
- ضع ملفات MP3 في مجلد `sounds/`
- أضف الأصوات الجديدة في قائمة الإعدادات

### إضافة خلفيات جديدة
- ضع الصور في مجلد `backgrounds/`
- أضف الخلفيات في قائمة الاختيار

## 🐛 حل المشاكل الشائعة

### المواقيت غير دقيقة
- تأكد من اختيار المدينة الصحيحة
- جرب طريقة حساب مختلفة
- استخدم التعديل اليدوي للدقة

### الأذان لا يعمل
- تأكد من تفعيل الصوت في المتصفح
- اضغط على الشاشة أولاً (متطلب المتصفحات الحديثة)
- تحقق من إعدادات الصوت

### التطبيق لا يعمل على الهاتف
- تأكد من استخدام HTTPS
- جرب متصفح مختلف
- امسح cache المتصفح

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. عمل commit للتغييرات
4. إرسال Pull Request

## 📞 الدعم

للدعم والاستفسارات:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

---

**تم تطوير هذا التطبيق بحب لخدمة المساجد والمصلين** 🕌❤️
