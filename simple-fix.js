// إصلاح بسيط ومباشر
console.log('🔧 إصلاح بسيط ومباشر...');

// إيقاف جميع الإصلاحات السابقة
window.stopAllFixes = true;

// دالة بسيطة لحفظ مدة الإقامة
function saveIqamahSimple() {
    console.log('💾 حفظ مدة الإقامة بسيط...');
    
    const times = {
        fajr: parseInt(document.getElementById('fajr-iqama-duration')?.value) || 10,
        dhuhr: parseInt(document.getElementById('dhuhr-iqama-duration')?.value) || 10,
        asr: parseInt(document.getElementById('asr-iqama-duration')?.value) || 10,
        maghrib: parseInt(document.getElementById('maghrib-iqama-duration')?.value) || 10,
        isha: parseInt(document.getElementById('isha-iqama-duration')?.value) || 10
    };
    
    localStorage.setItem('iqama_durations', JSON.stringify(times));
    console.log('✅ تم حفظ مدة الإقامة:', times);
    alert('تم حفظ مدة الإقامة:\n' + Object.keys(times).map(p => `${p}: ${times[p]} دقيقة`).join('\n'));
}

// دالة بسيطة لحفظ مدة التعتيم
function saveDarknessSimple() {
    console.log('💾 حفظ مدة التعتيم بسيط...');
    
    const times = {
        fajr: parseInt(document.getElementById('fajr-darkness')?.value) || 10,
        dhuhr: parseInt(document.getElementById('dhuhr-darkness')?.value) || 10,
        asr: parseInt(document.getElementById('asr-darkness')?.value) || 10,
        maghrib: parseInt(document.getElementById('maghrib-darkness')?.value) || 10,
        isha: parseInt(document.getElementById('isha-darkness')?.value) || 10
    };
    
    localStorage.setItem('darknessTimes', JSON.stringify(times));
    console.log('✅ تم حفظ مدة التعتيم:', times);
    alert('تم حفظ مدة التعتيم:\n' + Object.keys(times).map(p => `${p}: ${times[p]} دقيقة`).join('\n'));
}

// إصلاح الأزرار مباشرة
function fixButtonsSimple() {
    console.log('🔧 إصلاح الأزرار مباشرة...');
    
    // البحث عن زر حفظ الإقامة
    const iqamahBtn = document.getElementById('save-iqamah-times');
    if (iqamahBtn) {
        iqamahBtn.onclick = saveIqamahSimple;
        console.log('✅ تم ربط زر حفظ الإقامة');
    }
    
    // البحث عن زر حفظ التعتيم
    const darknessBtn = document.getElementById('save-darkness-times');
    if (darknessBtn) {
        darknessBtn.onclick = saveDarknessSimple;
        console.log('✅ تم ربط زر حفظ التعتيم');
    }
}

// تحميل الإعدادات المحفوظة
function loadSettingsSimple() {
    console.log('📁 تحميل الإعدادات...');
    
    // تحميل مدة الإقامة
    try {
        const saved = localStorage.getItem('iqama_durations');
        if (saved) {
            const times = JSON.parse(saved);
            Object.keys(times).forEach(prayer => {
                const input = document.getElementById(`${prayer}-iqama-duration`);
                if (input) input.value = times[prayer];
            });
            console.log('✅ تم تحميل مدة الإقامة');
        }
    } catch (e) {}
    
    // تحميل مدة التعتيم
    try {
        const saved = localStorage.getItem('darknessTimes');
        if (saved) {
            const times = JSON.parse(saved);
            Object.keys(times).forEach(prayer => {
                const input = document.getElementById(`${prayer}-darkness`);
                if (input) input.value = times[prayer];
            });
            console.log('✅ تم تحميل مدة التعتيم');
        }
    } catch (e) {}
}

// اختبار بسيط
function testSimple() {
    console.log('🧪 اختبار بسيط...');
    
    // فحص الحقول
    const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    
    console.log('📊 حقول الإقامة:');
    prayers.forEach(prayer => {
        const input = document.getElementById(`${prayer}-iqama-duration`);
        console.log(`  ${prayer}: ${input ? input.value || 'فارغ' : 'غير موجود'}`);
    });
    
    console.log('📊 حقول التعتيم:');
    prayers.forEach(prayer => {
        const input = document.getElementById(`${prayer}-darkness`);
        console.log(`  ${prayer}: ${input ? input.value || 'فارغ' : 'غير موجود'}`);
    });
    
    // فحص localStorage
    console.log('📊 التخزين المحلي:');
    console.log('  الإقامة:', localStorage.getItem('iqama_durations'));
    console.log('  التعتيم:', localStorage.getItem('darknessTimes'));
    
    // فحص الأزرار
    const iqamahBtn = document.getElementById('save-iqamah-times');
    const darknessBtn = document.getElementById('save-darkness-times');
    console.log('📊 الأزرار:');
    console.log('  زر الإقامة:', iqamahBtn ? 'موجود' : 'غير موجود');
    console.log('  زر التعتيم:', darknessBtn ? 'موجود' : 'غير موجود');
}

// تشغيل الإصلاح البسيط
function runSimpleFix() {
    console.log('🚀 تشغيل الإصلاح البسيط...');
    
    loadSettingsSimple();
    fixButtonsSimple();
    
    // اختبار بعد ثانية
    setTimeout(testSimple, 1000);
    
    console.log('✅ تم تطبيق الإصلاح البسيط');
    
    // إشعار
    const msg = document.createElement('div');
    msg.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-weight: bold;
    `;
    msg.textContent = '✅ تم تطبيق الإصلاح البسيط';
    document.body.appendChild(msg);
    setTimeout(() => msg.remove(), 3000);
}

// إضافة اختصار للاختبار
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 't') {
        testSimple();
    }
});

// تشغيل الإصلاح
setTimeout(runSimpleFix, 2000);

console.log('✅ تم تحميل الإصلاح البسيط - اضغط Ctrl+T للاختبار');
