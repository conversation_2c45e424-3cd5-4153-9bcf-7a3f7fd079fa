// تشخيص متقدم للمشاكل
console.log('🔍 بدء التشخيص المتقدم...');

function advancedDiagnosis() {
    console.log('='.repeat(50));
    console.log('🔍 تقرير التشخيص المتقدم');
    console.log('='.repeat(50));
    
    // 1. فحص حقول التعتيم
    console.log('📊 1. فحص حقول التعتيم:');
    const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    prayers.forEach(prayer => {
        const input = document.getElementById(`${prayer}-darkness`);
        if (input) {
            console.log(`  ✅ ${prayer}: ${input.value || 'فارغ'} (ID: ${input.id})`);
        } else {
            console.log(`  ❌ ${prayer}: حقل غير موجود`);
        }
    });
    
    // 2. فحص localStorage
    console.log('\n📊 2. فحص التخزين المحلي:');
    const savedDarkness = localStorage.getItem('darknessTimes');
    if (savedDarkness) {
        try {
            const parsed = JSON.parse(savedDarkness);
            console.log('  ✅ darknessTimes موجود:', parsed);
        } catch (e) {
            console.log('  ❌ خطأ في تحليل darknessTimes:', e);
        }
    } else {
        console.log('  ❌ darknessTimes غير موجود في localStorage');
    }
    
    // 3. فحص الأزرار
    console.log('\n📊 3. فحص الأزرار:');
    const saveBtn = document.getElementById('save-darkness-times');
    if (saveBtn) {
        console.log('  ✅ زر حفظ التعتيم موجود');
        console.log('  📝 onclick:', saveBtn.onclick ? 'مربوط' : 'غير مربوط');
    } else {
        console.log('  ❌ زر حفظ التعتيم غير موجود');
    }
    
    // 4. فحص المتغيرات العالمية
    console.log('\n📊 4. فحص المتغيرات العالمية:');
    console.log('  window.darknessTimes:', window.darknessTimes);
    console.log('  window.startIqamahCountdown:', typeof window.startIqamahCountdown);
    
    // 5. فحص جميع عناصر select و input
    console.log('\n📊 5. فحص جميع عناصر الإدخال:');
    const allInputs = document.querySelectorAll('input[type="number"], input[id*="darkness"]');
    allInputs.forEach(input => {
        console.log(`  📝 ${input.id || input.name || 'بدون ID'}: ${input.value || 'فارغ'}`);
    });
    
    // 6. محاولة حفظ تجريبية
    console.log('\n📊 6. اختبار حفظ تجريبي:');
    try {
        const testData = { asr: 5, fajr: 3 };
        localStorage.setItem('darknessTimes', JSON.stringify(testData));
        console.log('  ✅ تم حفظ البيانات التجريبية:', testData);
        
        const retrieved = JSON.parse(localStorage.getItem('darknessTimes'));
        console.log('  ✅ تم استرجاع البيانات:', retrieved);
    } catch (e) {
        console.log('  ❌ خطأ في الحفظ التجريبي:', e);
    }
    
    console.log('='.repeat(50));
    console.log('✅ انتهى التشخيص المتقدم');
    console.log('='.repeat(50));
    
    // إنشاء تقرير مرئي
    createVisualDiagnosisReport();
}

function createVisualDiagnosisReport() {
    // إزالة التقرير السابق
    const existing = document.getElementById('advanced-diagnosis-report');
    if (existing) existing.remove();
    
    const report = document.createElement('div');
    report.id = 'advanced-diagnosis-report';
    report.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.95);
        color: white;
        padding: 20px;
        border-radius: 10px;
        z-index: 10000;
        max-width: 500px;
        max-height: 80vh;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        border: 2px solid #FF9800;
    `;
    
    let html = '<h3 style="color: #FF9800; margin-top: 0;">🔍 تقرير التشخيص المتقدم</h3>';
    
    // فحص حقول التعتيم
    html += '<h4 style="color: #40E0D0;">🌙 حقول التعتيم:</h4>';
    const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    prayers.forEach(prayer => {
        const input = document.getElementById(`${prayer}-darkness`);
        const status = input ? `✅ ${input.value || 'فارغ'}` : '❌ غير موجود';
        html += `<p style="margin: 5px 0;">${prayer}: ${status}</p>`;
    });
    
    // فحص localStorage
    html += '<h4 style="color: #40E0D0;">💾 التخزين المحلي:</h4>';
    const savedDarkness = localStorage.getItem('darknessTimes');
    if (savedDarkness) {
        html += `<p style="color: #4CAF50;">✅ darknessTimes: ${savedDarkness}</p>`;
    } else {
        html += `<p style="color: #f44336;">❌ darknessTimes غير موجود</p>`;
    }
    
    // فحص الأزرار
    html += '<h4 style="color: #40E0D0;">🔘 الأزرار:</h4>';
    const saveBtn = document.getElementById('save-darkness-times');
    const saveBtnStatus = saveBtn ? '✅ موجود' : '❌ غير موجود';
    html += `<p>زر الحفظ: ${saveBtnStatus}</p>`;
    
    // أزرار التحكم
    html += '<hr style="border-color: #40E0D0;">';
    html += '<button onclick="testSaveDarkness()" style="background: #4CAF50; color: white; border: none; padding: 8px 12px; border-radius: 4px; margin: 5px; cursor: pointer;">اختبار الحفظ</button>';
    html += '<button onclick="testLoadDarkness()" style="background: #2196F3; color: white; border: none; padding: 8px 12px; border-radius: 4px; margin: 5px; cursor: pointer;">اختبار التحميل</button>';
    html += '<button onclick="this.parentElement.remove()" style="background: #f44336; color: white; border: none; padding: 8px 12px; border-radius: 4px; margin: 5px; cursor: pointer;">إغلاق</button>';
    
    report.innerHTML = html;
    document.body.appendChild(report);
}

// دالة اختبار الحفظ
function testSaveDarkness() {
    console.log('🧪 اختبار حفظ التعتيم...');
    
    const testData = {
        fajr: 3,
        dhuhr: 5,
        asr: 2,
        maghrib: 7,
        isha: 4
    };
    
    try {
        localStorage.setItem('darknessTimes', JSON.stringify(testData));
        console.log('✅ تم حفظ البيانات التجريبية:', testData);
        alert('✅ تم حفظ البيانات التجريبية بنجاح!\n' + JSON.stringify(testData, null, 2));
        
        // تحديث الحقول
        Object.keys(testData).forEach(prayer => {
            const input = document.getElementById(`${prayer}-darkness`);
            if (input) {
                input.value = testData[prayer];
            }
        });
        
    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات:', error);
        alert('❌ خطأ في حفظ البيانات: ' + error.message);
    }
}

// دالة اختبار التحميل
function testLoadDarkness() {
    console.log('🧪 اختبار تحميل التعتيم...');
    
    try {
        const saved = localStorage.getItem('darknessTimes');
        if (saved) {
            const data = JSON.parse(saved);
            console.log('✅ تم تحميل البيانات:', data);
            alert('✅ البيانات المحفوظة:\n' + JSON.stringify(data, null, 2));
        } else {
            console.log('❌ لا توجد بيانات محفوظة');
            alert('❌ لا توجد بيانات محفوظة');
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
        alert('❌ خطأ في تحميل البيانات: ' + error.message);
    }
}

// إضافة الدوال للنطاق العالمي
window.testSaveDarkness = testSaveDarkness;
window.testLoadDarkness = testLoadDarkness;

// إضافة اختصار
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        advancedDiagnosis();
    }
});

// تشغيل التشخيص تلقائياً
setTimeout(advancedDiagnosis, 3000);

console.log('✅ تم تحميل التشخيص المتقدم - اضغط Ctrl+Shift+D للتشخيص');
