// إصلاح مباشر لمدة التعتيم
console.log('🌙 بدء إصلاح مدة التعتيم المباشر...');

// دالة للحصول على مدة التعتيم الصحيحة
function getCorrectDarknessDuration(prayerName) {
    console.log(`🔍 البحث عن مدة التعتيم لصلاة ${prayerName}...`);
    
    // 1. محاولة الحصول من localStorage مباشرة
    try {
        const savedDarkness = localStorage.getItem('darknessTimes');
        if (savedDarkness) {
            const darknessTimes = JSON.parse(savedDarkness);
            if (darknessTimes[prayerName]) {
                console.log(`✅ تم العثور على مدة التعتيم من localStorage: ${darknessTimes[prayerName]} دقيقة`);
                return parseInt(darknessTimes[prayerName]);
            }
        }
    } catch (error) {
        console.error('خطأ في قراءة localStorage:', error);
    }
    
    // 2. محاولة الحصول من المتغير العالمي
    if (window.darknessTimes && window.darknessTimes[prayerName]) {
        console.log(`✅ تم العثور على مدة التعتيم من المتغير العالمي: ${window.darknessTimes[prayerName]} دقيقة`);
        return parseInt(window.darknessTimes[prayerName]);
    }
    
    // 3. محاولة الحصول من حقل الإدخال مباشرة
    const inputElement = document.getElementById(`${prayerName}-darkness`);
    if (inputElement && inputElement.value) {
        const value = parseInt(inputElement.value);
        console.log(`✅ تم العثور على مدة التعتيم من حقل الإدخال: ${value} دقيقة`);
        return value;
    }
    
    // 4. القيمة الافتراضية
    console.log(`⚠️ لم يتم العثور على مدة التعتيم، استخدام القيمة الافتراضية: 10 دقائق`);
    return 10;
}

// إعادة تعريف دالة بدء العد التنازلي للإقامة
function overrideIqamahCountdown() {
    console.log('🔧 إعادة تعريف دالة العد التنازلي للإقامة...');
    
    // حفظ الدالة الأصلية
    if (typeof window.originalStartIqamahCountdown === 'undefined') {
        window.originalStartIqamahCountdown = window.startIqamahCountdown;
    }
    
    // إعادة تعريف الدالة
    window.startIqamahCountdown = function(prayerName, arabicName) {
        console.log(`🕌 بدء العد التنازلي للإقامة - ${arabicName}`);
        console.log(`🔍 اسم الصلاة: ${prayerName}`);
        
        // الحصول على مدة التعتيم الصحيحة
        const correctDarknessDuration = getCorrectDarknessDuration(prayerName);
        console.log(`🌙 مدة التعتيم المحددة: ${correctDarknessDuration} دقيقة`);
        
        // تحديث المتغير العالمي
        if (!window.darknessTimes) {
            window.darknessTimes = {};
        }
        window.darknessTimes[prayerName] = correctDarknessDuration;
        
        // استدعاء الدالة الأصلية
        if (window.originalStartIqamahCountdown) {
            return window.originalStartIqamahCountdown.call(this, prayerName, arabicName);
        }
    };
    
    console.log('✅ تم إعادة تعريف دالة العد التنازلي للإقامة');
}

// إصلاح مباشر لكود التعتيم الموجود
function patchDarknessCode() {
    console.log('🔧 إصلاح كود التعتيم الموجود...');
    
    // البحث عن جميع الأماكن التي تستخدم darknessMinutes
    const scriptElements = document.querySelectorAll('script');
    
    scriptElements.forEach((script, index) => {
        if (script.innerHTML.includes('darknessMinutes') && script.innerHTML.includes('window.darknessTimes')) {
            console.log(`🔍 تم العثور على كود التعتيم في script ${index}`);
            
            // إضافة كود إصلاح مباشر
            const patchScript = document.createElement('script');
            patchScript.innerHTML = `
                // إصلاح مباشر لمدة التعتيم
                (function() {
                    const originalCode = ${script.innerHTML};
                    
                    // إعادة تعريف الحصول على مدة التعتيم
                    window.getDarknessDuration = function(prayerName) {
                        // محاولة الحصول من localStorage
                        try {
                            const saved = localStorage.getItem('darknessTimes');
                            if (saved) {
                                const times = JSON.parse(saved);
                                if (times[prayerName]) {
                                    console.log('🌙 مدة التعتيم من localStorage:', times[prayerName]);
                                    return parseInt(times[prayerName]);
                                }
                            }
                        } catch (e) {}
                        
                        // محاولة الحصول من حقل الإدخال
                        const input = document.getElementById(prayerName + '-darkness');
                        if (input && input.value) {
                            console.log('🌙 مدة التعتيم من الحقل:', input.value);
                            return parseInt(input.value);
                        }
                        
                        console.log('🌙 مدة التعتيم الافتراضية: 10');
                        return 10;
                    };
                })();
            `;
            
            document.head.appendChild(patchScript);
        }
    });
}

// دالة لإجبار تحديث مدة التعتيم
function forceDarknessUpdate() {
    console.log('🔄 إجبار تحديث مدة التعتيم...');
    
    // تحديث المتغير العالمي من localStorage
    try {
        const savedDarkness = localStorage.getItem('darknessTimes');
        if (savedDarkness) {
            window.darknessTimes = JSON.parse(savedDarkness);
            console.log('✅ تم تحديث المتغير العالمي:', window.darknessTimes);
        } else {
            // إنشاء متغير افتراضي
            window.darknessTimes = {
                fajr: 10,
                dhuhr: 10,
                asr: 10,
                maghrib: 10,
                isha: 10
            };
            console.log('✅ تم إنشاء متغير افتراضي:', window.darknessTimes);
        }
    } catch (error) {
        console.error('خطأ في تحديث مدة التعتيم:', error);
    }
}

// دالة اختبار مدة التعتيم
function testDarknessDuration() {
    console.log('🧪 اختبار مدة التعتيم...');
    
    const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    
    prayers.forEach(prayer => {
        const duration = getCorrectDarknessDuration(prayer);
        console.log(`${prayer}: ${duration} دقيقة`);
        
        // فحص حقل الإدخال
        const input = document.getElementById(`${prayer}-darkness`);
        if (input) {
            console.log(`حقل ${prayer}: ${input.value || 'فارغ'}`);
        } else {
            console.log(`حقل ${prayer}: غير موجود`);
        }
    });
    
    // فحص localStorage
    const saved = localStorage.getItem('darknessTimes');
    console.log('localStorage darknessTimes:', saved);
    
    // فحص المتغير العالمي
    console.log('window.darknessTimes:', window.darknessTimes);
}

// دالة لإصلاح زر الحفظ
function fixSaveButton() {
    console.log('🔧 إصلاح زر حفظ مدة التعتيم...');
    
    const saveBtn = document.getElementById('save-darkness-times');
    if (saveBtn) {
        // إزالة المستمعات السابقة
        const newSaveBtn = saveBtn.cloneNode(true);
        saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);
        
        newSaveBtn.addEventListener('click', function() {
            console.log('💾 حفظ مدة التعتيم...');
            
            const darknessTimes = {};
            const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
            
            prayers.forEach(prayer => {
                const input = document.getElementById(`${prayer}-darkness`);
                if (input) {
                    const value = parseInt(input.value) || 10;
                    darknessTimes[prayer] = value;
                    console.log(`${prayer}: ${value} دقيقة`);
                }
            });
            
            // حفظ في localStorage
            localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
            
            // تحديث المتغير العالمي
            window.darknessTimes = darknessTimes;
            
            console.log('✅ تم حفظ مدة التعتيم:', darknessTimes);
            
            // إشعار
            alert('تم حفظ مدة التعتيم بنجاح!\n' + 
                  Object.keys(darknessTimes).map(p => `${p}: ${darknessTimes[p]} دقيقة`).join('\n'));
            
            // إجبار تحديث
            forceDarknessUpdate();
        });
        
        console.log('✅ تم إصلاح زر الحفظ');
    }
}

// تشغيل جميع الإصلاحات
function runAllDarknessFixes() {
    console.log('🚀 تشغيل جميع إصلاحات مدة التعتيم...');
    
    forceDarknessUpdate();
    overrideIqamahCountdown();
    patchDarknessCode();
    fixSaveButton();
    
    console.log('✅ تم تطبيق جميع إصلاحات مدة التعتيم');
    
    // اختبار
    setTimeout(testDarknessDuration, 1000);
}

// إضافة اختصار للاختبار
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        testDarknessDuration();
    }
});

// تشغيل الإصلاحات
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(runAllDarknessFixes, 3000);
    });
} else {
    setTimeout(runAllDarknessFixes, 3000);
}

console.log('✅ تم تحميل إصلاح مدة التعتيم المباشر - اضغط Ctrl+Shift+D للاختبار');
