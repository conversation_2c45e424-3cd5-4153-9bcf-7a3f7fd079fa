// ملف تشخيص المشاكل
console.log('🔍 بدء تشخيص المشاكل...');

function diagnoseIssues() {
    const issues = [];
    
    // فحص الساعة الرقمية
    const digitalClock = document.querySelector('.digital-clock');
    if (!digitalClock) {
        issues.push('❌ عنصر الساعة الرقمية غير موجود');
    } else {
        console.log('✅ عنصر الساعة الرقمية موجود');
        console.log('📊 محتوى الساعة:', digitalClock.textContent);
        console.log('🎨 أنماط الساعة:', window.getComputedStyle(digitalClock));
    }
    
    // فحص اختيار الخلفية
    const backgroundSelect = document.getElementById('backgroundSelect');
    if (!backgroundSelect) {
        issues.push('❌ عنصر اختيار الخلفية غير موجود');
    } else {
        console.log('✅ عنصر اختيار الخلفية موجود');
        console.log('📊 قيمة الخلفية المختارة:', backgroundSelect.value);
        console.log('📊 خيارات الخلفية:', backgroundSelect.options.length);
    }
    
    // فحص مواقيت الصلاة
    const prayerTimeElements = [
        'fajr-time', 'sunrise-time', 'dhuhr-time', 
        'asr-time', 'maghrib-time', 'isha-time'
    ];
    
    prayerTimeElements.forEach(id => {
        const element = document.getElementById(id);
        if (!element) {
            issues.push(`❌ عنصر ${id} غير موجود`);
        } else {
            console.log(`✅ ${id}: ${element.textContent}`);
        }
    });
    
    // فحص أزرار التعديل اليدوي
    const saveBtn = document.getElementById('save-manual-times');
    const resetBtn = document.getElementById('reset-manual-times');
    
    if (!saveBtn) {
        issues.push('❌ زر حفظ المواقيت غير موجود');
    } else {
        console.log('✅ زر حفظ المواقيت موجود');
    }
    
    if (!resetBtn) {
        issues.push('❌ زر إعادة ضبط المواقيت غير موجود');
    } else {
        console.log('✅ زر إعادة ضبط المواقيت موجود');
    }
    
    // فحص حقول التعديل اليدوي
    const manualInputs = [
        'manual-fajr', 'manual-sunrise', 'manual-dhuhr',
        'manual-asr', 'manual-maghrib', 'manual-isha'
    ];
    
    manualInputs.forEach(id => {
        const input = document.getElementById(id);
        if (!input) {
            issues.push(`❌ حقل ${id} غير موجود`);
        } else {
            console.log(`✅ ${id}: ${input.value || 'فارغ'}`);
        }
    });
    
    // فحص المكتبات
    if (typeof prayTimes === 'undefined') {
        issues.push('❌ مكتبة PrayTimes غير محملة');
    } else {
        console.log('✅ مكتبة PrayTimes محملة');
    }
    
    if (typeof moment === 'undefined') {
        issues.push('❌ مكتبة Moment غير محملة');
    } else {
        console.log('✅ مكتبة Moment محملة');
    }
    
    // فحص التخزين المحلي
    console.log('💾 بيانات التخزين المحلي:');
    console.log('- selectedCity:', localStorage.getItem('selectedCity'));
    console.log('- selectedBackground:', localStorage.getItem('selectedBackground'));
    console.log('- manualPrayerTimes:', localStorage.getItem('manualPrayerTimes'));
    console.log('- manualPrayerTimesEnabled:', localStorage.getItem('manualPrayerTimesEnabled'));
    
    // فحص الأخطاء في Console
    const originalError = console.error;
    const errors = [];
    console.error = function(...args) {
        errors.push(args.join(' '));
        originalError.apply(console, args);
    };
    
    // عرض النتائج
    if (issues.length > 0) {
        console.log('🚨 المشاكل المكتشفة:');
        issues.forEach(issue => console.log(issue));
    } else {
        console.log('🎉 لم يتم اكتشاف مشاكل واضحة');
    }
    
    // إنشاء تقرير مرئي
    createVisualReport(issues);
}

function createVisualReport(issues) {
    // إزالة التقرير السابق إن وجد
    const existingReport = document.getElementById('debug-report');
    if (existingReport) {
        existingReport.remove();
    }
    
    const report = document.createElement('div');
    report.id = 'debug-report';
    report.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 20px;
        border-radius: 10px;
        z-index: 10000;
        max-width: 400px;
        max-height: 80vh;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        border: 2px solid #40E0D0;
    `;
    
    let reportHTML = '<h3 style="color: #40E0D0; margin-top: 0;">🔍 تقرير التشخيص</h3>';
    
    if (issues.length === 0) {
        reportHTML += '<p style="color: #4CAF50;">🎉 لم يتم اكتشاف مشاكل واضحة</p>';
    } else {
        reportHTML += '<p style="color: #f44336;">🚨 المشاكل المكتشفة:</p><ul>';
        issues.forEach(issue => {
            reportHTML += `<li style="color: #ff9800; margin: 5px 0;">${issue}</li>`;
        });
        reportHTML += '</ul>';
    }
    
    // معلومات إضافية
    reportHTML += '<hr style="border-color: #40E0D0;">';
    reportHTML += '<h4 style="color: #40E0D0;">📊 معلومات النظام:</h4>';
    reportHTML += `<p>🌐 المتصفح: ${navigator.userAgent.split(' ')[0]}</p>`;
    reportHTML += `<p>📱 الجهاز: ${navigator.platform}</p>`;
    reportHTML += `<p>🔗 الرابط: ${window.location.href}</p>`;
    reportHTML += `<p>⏰ الوقت: ${new Date().toLocaleString('ar-SA')}</p>`;
    
    // زر إغلاق
    reportHTML += '<button onclick="this.parentElement.remove()" style="background: #f44336; color: white; border: none; padding: 10px; border-radius: 5px; margin-top: 10px; cursor: pointer;">إغلاق التقرير</button>';
    
    report.innerHTML = reportHTML;
    document.body.appendChild(report);
    
    // إخفاء التقرير تلقائياً بعد 30 ثانية
    setTimeout(() => {
        if (report.parentElement) {
            report.remove();
        }
    }, 30000);
}

// تشغيل التشخيص عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(diagnoseIssues, 2000);
    });
} else {
    setTimeout(diagnoseIssues, 2000);
}

// إضافة اختصار لوحة المفاتيح لتشغيل التشخيص
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        diagnoseIssues();
    }
});

console.log('✅ تم تحميل ملف التشخيص - اضغط Ctrl+Shift+D لتشغيل التشخيص');
