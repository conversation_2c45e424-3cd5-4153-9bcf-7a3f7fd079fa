// ملف إصلاح المشاكل الأساسية
console.log('🔧 بدء تحميل ملف إصلاح المشاكل...');

// إصلاح الساعة الرقمية
function fixDigitalClock() {
    console.log('🕐 إصلاح الساعة الرقمية...');
    
    // إيقاف جميع المؤقتات السابقة
    if (window.clockInterval) clearInterval(window.clockInterval);
    if (window.digitalClockInterval) clearInterval(window.digitalClockInterval);
    if (window.mainClockInterval) clearInterval(window.mainClockInterval);
    
    function updateClock() {
        const clockElement = document.querySelector('.digital-clock');
        if (!clockElement) return;
        
        const now = new Date();
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        
        clockElement.textContent = `${hours}:${minutes}:${seconds}`;
        clockElement.style.color = '#40E0D0';
        clockElement.style.fontSize = '2.2em';
        clockElement.style.fontWeight = 'bold';
    }
    
    // تحديث فوري
    updateClock();
    
    // مؤقت واحد فقط
    window.clockInterval = setInterval(updateClock, 1000);
    console.log('✅ تم إصلاح الساعة الرقمية');
}

// إصلاح الخلفيات
function fixBackgrounds() {
    console.log('🖼️ إصلاح نظام الخلفيات...');
    
    const backgroundSelect = document.getElementById('backgroundSelect');
    if (!backgroundSelect) {
        console.error('❌ لم يتم العثور على عنصر اختيار الخلفية');
        return;
    }
    
    // تحميل الخلفية المحفوظة
    const savedBackground = localStorage.getItem('selectedBackground');
    if (savedBackground) {
        backgroundSelect.value = savedBackground;
        applyBackground(savedBackground);
        console.log('📁 تم تحميل الخلفية المحفوظة:', savedBackground);
    }
    
    // إزالة المستمعات السابقة
    const newSelect = backgroundSelect.cloneNode(true);
    backgroundSelect.parentNode.replaceChild(newSelect, backgroundSelect);
    
    // إضافة مستمع جديد
    newSelect.addEventListener('change', function() {
        const selectedBg = this.value;
        console.log('🔄 تغيير الخلفية إلى:', selectedBg);
        applyBackground(selectedBg);
        localStorage.setItem('selectedBackground', selectedBg);
        showMessage('تم تغيير الخلفية بنجاح');
    });
    
    function applyBackground(bgPath) {
        if (bgPath && bgPath !== '') {
            document.body.style.backgroundImage = `url('${bgPath}')`;
            document.body.style.backgroundSize = 'cover';
            document.body.style.backgroundPosition = 'center';
            document.body.style.backgroundRepeat = 'no-repeat';
            document.body.style.backgroundAttachment = 'fixed';
            console.log('✅ تم تطبيق الخلفية:', bgPath);
        }
    }
    
    console.log('✅ تم إصلاح نظام الخلفيات');
}

// إصلاح المواقيت
function fixPrayerTimes() {
    console.log('🕌 إصلاح مواقيت الصلاة...');
    
    // مواقيت افتراضية صحيحة
    const defaultTimes = {
        fajr: '05:15',
        sunrise: '06:45',
        dhuhr: '12:30',
        asr: '15:45',
        maghrib: '18:15',
        isha: '19:45'
    };
    
    // تطبيق المواقيت على العناصر
    function updatePrayerDisplay(times) {
        const prayerElements = {
            'fajr-time': times.fajr,
            'sunrise-time': times.sunrise,
            'dhuhr-time': times.dhuhr,
            'asr-time': times.asr,
            'maghrib-time': times.maghrib,
            'isha-time': times.isha
        };
        
        Object.keys(prayerElements).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = prayerElements[id];
                console.log(`📅 تحديث ${id}: ${prayerElements[id]}`);
            }
        });
        
        // تحديث المستطيل الأفقي أيضاً
        const prayerNames = ['fajr', 'sunrise', 'dhuhr', 'asr', 'maghrib', 'isha'];
        prayerNames.forEach(prayer => {
            const elements = document.querySelectorAll(`[data-prayer="${prayer}"], .${prayer}-time, #${prayer}-time`);
            elements.forEach(el => {
                if (el && times[prayer]) {
                    el.textContent = times[prayer];
                }
            });
        });
    }
    
    // تطبيق المواقيت الافتراضية
    updatePrayerDisplay(defaultTimes);
    console.log('✅ تم تطبيق المواقيت الافتراضية');
    
    // محاولة تحديث المواقيت الحقيقية
    setTimeout(() => {
        if (typeof getPrayerTimes === 'function') {
            try {
                getPrayerTimes();
                console.log('✅ تم تحديث المواقيت الحقيقية');
            } catch (error) {
                console.warn('⚠️ فشل تحديث المواقيت الحقيقية، استخدام الافتراضية');
            }
        }
    }, 2000);
}

// إصلاح التعديل اليدوي
function fixManualEdit() {
    console.log('✏️ إصلاح التعديل اليدوي...');
    
    const saveBtn = document.getElementById('save-manual-times');
    const resetBtn = document.getElementById('reset-manual-times');
    
    if (saveBtn) {
        // إزالة المستمعات السابقة
        const newSaveBtn = saveBtn.cloneNode(true);
        saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);
        
        newSaveBtn.addEventListener('click', function() {
            console.log('💾 حفظ المواقيت المعدلة...');
            
            const prayers = ['fajr', 'sunrise', 'dhuhr', 'asr', 'maghrib', 'isha'];
            const manualTimes = {};
            
            prayers.forEach(prayer => {
                const input = document.getElementById(`manual-${prayer}`);
                if (input && input.value) {
                    manualTimes[prayer] = input.value;
                }
            });
            
            if (Object.keys(manualTimes).length > 0) {
                localStorage.setItem('manualPrayerTimes', JSON.stringify(manualTimes));
                localStorage.setItem('manualPrayerTimesEnabled', 'true');
                
                // تطبيق المواقيت المعدلة
                const prayerElements = {
                    'fajr-time': manualTimes.fajr,
                    'sunrise-time': manualTimes.sunrise,
                    'dhuhr-time': manualTimes.dhuhr,
                    'asr-time': manualTimes.asr,
                    'maghrib-time': manualTimes.maghrib,
                    'isha-time': manualTimes.isha
                };
                
                Object.keys(prayerElements).forEach(id => {
                    const element = document.getElementById(id);
                    if (element && prayerElements[id]) {
                        element.textContent = prayerElements[id];
                    }
                });
                
                showMessage('تم حفظ المواقيت المعدلة بنجاح');
                console.log('✅ تم حفظ المواقيت:', manualTimes);
            }
        });
    }
    
    if (resetBtn) {
        // إزالة المستمعات السابقة
        const newResetBtn = resetBtn.cloneNode(true);
        resetBtn.parentNode.replaceChild(newResetBtn, resetBtn);
        
        newResetBtn.addEventListener('click', function() {
            if (confirm('هل تريد إعادة ضبط المواقيت؟')) {
                localStorage.removeItem('manualPrayerTimes');
                localStorage.setItem('manualPrayerTimesEnabled', 'false');
                
                // إعادة تحميل الصفحة لتطبيق المواقيت الأصلية
                location.reload();
            }
        });
    }
    
    console.log('✅ تم إصلاح التعديل اليدوي');
}

// دالة عرض الرسائل
function showMessage(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 10000;
        font-weight: bold;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    `;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// تشغيل جميع الإصلاحات
function runAllFixes() {
    console.log('🚀 بدء تشغيل جميع الإصلاحات...');
    
    // تأخير قصير للتأكد من تحميل DOM
    setTimeout(() => {
        fixDigitalClock();
        fixBackgrounds();
        fixPrayerTimes();
        fixManualEdit();
        
        console.log('🎉 تم تطبيق جميع الإصلاحات بنجاح!');
        showMessage('تم إصلاح جميع المشاكل بنجاح');
    }, 1000);
}

// تشغيل الإصلاحات عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllFixes);
} else {
    runAllFixes();
}

console.log('✅ تم تحميل ملف إصلاح المشاكل بنجاح');
