// الإصلاح الشامل والنهائي لجميع المشاكل
console.log('🔧 بدء الإصلاح الشامل والنهائي...');

// إيقاف جميع الإصلاحات السابقة والرسائل المزعجة
window.stopAllPreviousFixes = true;

// إزالة جميع الرسائل المزعجة
function removeAnnoyingMessages() {
    // إزالة جميع الإشعارات المزعجة
    const notifications = document.querySelectorAll('[style*="position: fixed"]');
    notifications.forEach(notification => {
        if (notification.textContent.includes('تم إصلاح') || 
            notification.textContent.includes('تم تطبيق') ||
            notification.textContent.includes('بنجاح')) {
            notification.remove();
        }
    });
    
    // إزالة التقارير المزعجة
    const reports = document.querySelectorAll('#test-report, #debug-report, #advanced-diagnosis-report');
    reports.forEach(report => report.remove());
    
    console.log('✅ تم إزالة الرسائل المزعجة');
}

// إصلاح الساعة الرقمية
function fixDigitalClockUltimate() {
    console.log('🕐 إصلاح الساعة الرقمية...');
    
    // إيقاف جميع المؤقتات السابقة
    for (let i = 1; i < 99999; i++) {
        clearInterval(i);
    }
    
    function updateClockUltimate() {
        const clockElement = document.querySelector('.digital-clock');
        if (!clockElement) return;
        
        const now = new Date();
        const timeFormat = document.getElementById('time-format-select')?.value || '24';
        
        let hours = now.getHours();
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        
        if (timeFormat === '12') {
            const period = hours >= 12 ? 'م' : 'ص';
            hours = hours % 12 || 12;
            hours = String(hours).padStart(2, '0');
            clockElement.textContent = `${hours}:${minutes}:${seconds} ${period}`;
        } else {
            hours = String(hours).padStart(2, '0');
            clockElement.textContent = `${hours}:${minutes}:${seconds}`;
        }
    }
    
    updateClockUltimate();
    window.ultimateClockInterval = setInterval(updateClockUltimate, 1000);
    console.log('✅ تم إصلاح الساعة الرقمية');
}

// إصلاح الخلفيات
function fixBackgroundsUltimate() {
    console.log('🖼️ إصلاح الخلفيات...');
    
    const backgroundSelect = document.getElementById('backgroundSelect');
    if (backgroundSelect) {
        // إزالة المستمعات السابقة
        const newSelect = backgroundSelect.cloneNode(true);
        backgroundSelect.parentNode.replaceChild(newSelect, backgroundSelect);
        
        // إضافة مستمع جديد
        newSelect.addEventListener('change', function() {
            const selectedBg = this.value;
            console.log('🔄 تغيير الخلفية إلى:', selectedBg);
            
            if (selectedBg && selectedBg !== '') {
                document.body.style.backgroundImage = `url('${selectedBg}')`;
                document.body.style.backgroundSize = 'cover';
                document.body.style.backgroundPosition = 'center';
                document.body.style.backgroundRepeat = 'no-repeat';
                document.body.style.backgroundAttachment = 'fixed';
                localStorage.setItem('selectedBackground', selectedBg);
                console.log('✅ تم تغيير الخلفية');
            }
        });
        
        // تحميل الخلفية المحفوظة
        const savedBg = localStorage.getItem('selectedBackground');
        if (savedBg) {
            newSelect.value = savedBg;
            document.body.style.backgroundImage = `url('${savedBg}')`;
            document.body.style.backgroundSize = 'cover';
            document.body.style.backgroundPosition = 'center';
            document.body.style.backgroundRepeat = 'no-repeat';
            document.body.style.backgroundAttachment = 'fixed';
        }
        
        console.log('✅ تم إصلاح الخلفيات');
    }
}

// إصلاح حفظ مدة الإقامة
function fixIqamahSaveUltimate() {
    console.log('⏰ إصلاح حفظ مدة الإقامة...');
    
    const saveBtn = document.getElementById('save-iqamah-times');
    if (saveBtn) {
        const newBtn = saveBtn.cloneNode(true);
        saveBtn.parentNode.replaceChild(newBtn, saveBtn);
        
        newBtn.onclick = function() {
            const times = {
                fajr: parseInt(document.getElementById('fajr-iqama-duration')?.value) || 10,
                dhuhr: parseInt(document.getElementById('dhuhr-iqama-duration')?.value) || 10,
                asr: parseInt(document.getElementById('asr-iqama-duration')?.value) || 10,
                maghrib: parseInt(document.getElementById('maghrib-iqama-duration')?.value) || 10,
                isha: parseInt(document.getElementById('isha-iqama-duration')?.value) || 10
            };
            
            localStorage.setItem('iqama_durations', JSON.stringify(times));
            window.iqamahTimes = times;
            
            console.log('✅ تم حفظ مدة الإقامة:', times);
            alert('تم حفظ مدة الإقامة بنجاح');
        };
        
        console.log('✅ تم ربط زر حفظ مدة الإقامة');
    }
}

// إصلاح حفظ مدة التعتيم
function fixDarknessSaveUltimate() {
    console.log('🌙 إصلاح حفظ مدة التعتيم...');
    
    const saveBtn = document.getElementById('save-darkness-times');
    if (saveBtn) {
        const newBtn = saveBtn.cloneNode(true);
        saveBtn.parentNode.replaceChild(newBtn, saveBtn);
        
        newBtn.onclick = function() {
            const times = {
                fajr: parseInt(document.getElementById('fajr-darkness')?.value) || 10,
                dhuhr: parseInt(document.getElementById('dhuhr-darkness')?.value) || 10,
                asr: parseInt(document.getElementById('asr-darkness')?.value) || 10,
                maghrib: parseInt(document.getElementById('maghrib-darkness')?.value) || 10,
                isha: parseInt(document.getElementById('isha-darkness')?.value) || 10
            };
            
            localStorage.setItem('darknessTimes', JSON.stringify(times));
            window.darknessTimes = times;
            
            console.log('✅ تم حفظ مدة التعتيم:', times);
            alert('تم حفظ مدة التعتيم بنجاح');
        };
        
        console.log('✅ تم ربط زر حفظ مدة التعتيم');
    }
}

// إصلاح تشغيل الأذان
function fixAdhanPlayingUltimate() {
    console.log('🔊 إصلاح تشغيل الأذان...');
    
    // إنشاء دالة تشغيل أذان بديلة
    window.playAdhanUltimate = function() {
        console.log('🔊 تشغيل الأذان...');
        
        // محاولة تشغيل الملف الصوتي
        const adhanAudio = document.getElementById('adhan-audio');
        if (adhanAudio && adhanAudio.src) {
            adhanAudio.play().then(() => {
                console.log('✅ تم تشغيل الأذان من الملف');
            }).catch(() => {
                playFallbackSound();
            });
        } else {
            playFallbackSound();
        }
        
        function playFallbackSound() {
            // تشغيل صوت بديل
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                
                oscillator.start();
                oscillator.stop(audioContext.currentTime + 5);
                
                console.log('✅ تم تشغيل نغمة الأذان البديلة');
            } catch (error) {
                console.log('⚠️ لا يمكن تشغيل الصوت');
            }
        }
    };
    
    console.log('✅ تم إصلاح تشغيل الأذان');
}

// إصلاح تنسيق الوقت 12/24 ساعة
function fixTimeFormatUltimate() {
    console.log('🕐 إصلاح تنسيق الوقت...');
    
    const timeFormatSelect = document.getElementById('time-format-select');
    if (timeFormatSelect) {
        const newSelect = timeFormatSelect.cloneNode(true);
        timeFormatSelect.parentNode.replaceChild(newSelect, timeFormatSelect);
        
        newSelect.addEventListener('change', function() {
            const format = this.value;
            localStorage.setItem('timeFormat', format);
            console.log('✅ تم تغيير تنسيق الوقت إلى:', format);
            
            // تحديث الساعة فوراً
            fixDigitalClockUltimate();
        });
        
        // تحميل التنسيق المحفوظ
        const savedFormat = localStorage.getItem('timeFormat');
        if (savedFormat) {
            newSelect.value = savedFormat;
        }
        
        console.log('✅ تم إصلاح تنسيق الوقت');
    }
}

// تحميل الإعدادات المحفوظة
function loadSavedSettingsUltimate() {
    console.log('📁 تحميل الإعدادات المحفوظة...');
    
    // تحميل مدة الإقامة
    try {
        const savedIqamah = localStorage.getItem('iqama_durations');
        if (savedIqamah) {
            const times = JSON.parse(savedIqamah);
            Object.keys(times).forEach(prayer => {
                const input = document.getElementById(`${prayer}-iqama-duration`);
                if (input) input.value = times[prayer];
            });
            window.iqamahTimes = times;
            console.log('✅ تم تحميل مدة الإقامة');
        }
    } catch (e) {}
    
    // تحميل مدة التعتيم
    try {
        const savedDarkness = localStorage.getItem('darknessTimes');
        if (savedDarkness) {
            const times = JSON.parse(savedDarkness);
            Object.keys(times).forEach(prayer => {
                const input = document.getElementById(`${prayer}-darkness`);
                if (input) input.value = times[prayer];
            });
            window.darknessTimes = times;
            console.log('✅ تم تحميل مدة التعتيم');
        }
    } catch (e) {}
    
    // تحميل تنسيق الوقت
    const savedFormat = localStorage.getItem('timeFormat');
    if (savedFormat) {
        const formatSelect = document.getElementById('time-format-select');
        if (formatSelect) formatSelect.value = savedFormat;
    }
}

// اختبار سريع للأذان
function testAdhanUltimate() {
    console.log('🧪 اختبار الأذان...');
    
    if (window.playAdhanUltimate) {
        window.playAdhanUltimate();
        
        // إشعار بسيط
        const msg = document.createElement('div');
        msg.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #4CAF50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 10000;
            font-size: 18px;
        `;
        msg.textContent = '🔊 تم تشغيل اختبار الأذان';
        document.body.appendChild(msg);
        setTimeout(() => msg.remove(), 3000);
    }
}

// تشغيل جميع الإصلاحات
function runUltimateFix() {
    console.log('🚀 تشغيل الإصلاح الشامل والنهائي...');
    
    // إزالة الرسائل المزعجة أولاً
    removeAnnoyingMessages();
    
    // تطبيق جميع الإصلاحات
    setTimeout(() => {
        loadSavedSettingsUltimate();
        fixDigitalClockUltimate();
        fixBackgroundsUltimate();
        fixIqamahSaveUltimate();
        fixDarknessSaveUltimate();
        fixAdhanPlayingUltimate();
        fixTimeFormatUltimate();
        
        console.log('✅ تم تطبيق جميع الإصلاحات بنجاح');
        
        // إشعار نهائي بسيط
        const finalMsg = document.createElement('div');
        finalMsg.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-weight: bold;
        `;
        finalMsg.textContent = '✅ تم إصلاح التطبيق';
        document.body.appendChild(finalMsg);
        setTimeout(() => finalMsg.remove(), 5000);
        
    }, 1000);
    
    // إزالة الرسائل المزعجة كل 5 ثوانِ
    setInterval(removeAnnoyingMessages, 5000);
}

// إضافة اختصار لاختبار الأذان
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'a') {
        testAdhanUltimate();
    }
});

// تشغيل الإصلاح الشامل
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(runUltimateFix, 2000);
    });
} else {
    setTimeout(runUltimateFix, 2000);
}

console.log('✅ تم تحميل الإصلاح الشامل والنهائي - اضغط Ctrl+A لاختبار الأذان');
