// إصلاح نهائي وشامل لمدة التعتيم
console.log('🌙 بدء الإصلاح النهائي لمدة التعتيم...');

// دالة للحصول على مدة التعتيم الصحيحة
function getFinalDarknessDuration(prayerName) {
    console.log(`🔍 البحث النهائي عن مدة التعتيم لصلاة ${prayerName}...`);
    
    let duration = 10; // القيمة الافتراضية
    
    // 1. البحث في localStorage
    try {
        const savedDarkness = localStorage.getItem('darknessTimes');
        if (savedDarkness) {
            const darknessTimes = JSON.parse(savedDarkness);
            if (darknessTimes[prayerName]) {
                duration = parseInt(darknessTimes[prayerName]);
                console.log(`✅ تم العثور على مدة التعتيم من localStorage: ${duration} دقيقة`);
                return duration;
            }
        }
    } catch (error) {
        console.error('خطأ في قراءة localStorage:', error);
    }
    
    // 2. البحث في المتغير العالمي
    if (window.darknessTimes && window.darknessTimes[prayerName]) {
        duration = parseInt(window.darknessTimes[prayerName]);
        console.log(`✅ تم العثور على مدة التعتيم من المتغير العالمي: ${duration} دقيقة`);
        return duration;
    }
    
    // 3. البحث في حقول الإدخال
    const possibleIds = [
        `${prayerName}-darkness`,
        `${prayerName}-darkness-duration`,
        `${prayerName}_darkness`
    ];
    
    for (const id of possibleIds) {
        const input = document.getElementById(id);
        if (input && input.value) {
            duration = parseInt(input.value);
            console.log(`✅ تم العثور على مدة التعتيم من حقل ${id}: ${duration} دقيقة`);
            return duration;
        }
    }
    
    console.log(`⚠️ لم يتم العثور على مدة التعتيم، استخدام القيمة الافتراضية: ${duration} دقيقة`);
    return duration;
}

// إعادة تعريف شاملة لدالة بدء العد التنازلي للإقامة
function overrideFinalIqamahCountdown() {
    console.log('🔧 إعادة تعريف نهائية لدالة العد التنازلي للإقامة...');
    
    // حفظ الدالة الأصلية إذا لم تكن محفوظة
    if (typeof window.originalStartIqamahCountdownFinal === 'undefined') {
        window.originalStartIqamahCountdownFinal = window.startIqamahCountdown;
    }
    
    // إعادة تعريف الدالة بشكل نهائي
    window.startIqamahCountdown = function(prayerName, arabicName) {
        console.log(`🕌 بدء العد التنازلي النهائي للإقامة - ${arabicName}`);
        console.log(`🔍 اسم الصلاة: ${prayerName}`);
        
        // الحصول على مدة التعتيم الصحيحة
        const correctDarknessDuration = getFinalDarknessDuration(prayerName);
        console.log(`🌙 مدة التعتيم المحددة نهائياً: ${correctDarknessDuration} دقيقة`);
        
        // تحديث جميع المتغيرات العالمية
        if (!window.darknessTimes) {
            window.darknessTimes = {};
        }
        window.darknessTimes[prayerName] = correctDarknessDuration;
        
        // تحديث متغير darknessMinutes المستخدم في الكود
        window.darknessMinutes = correctDarknessDuration;
        
        // إشعار مرئي للمستخدم
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 50px;
            right: 20px;
            background: #9C27B0;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-weight: bold;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        notification.textContent = `🌙 مدة التعتيم: ${correctDarknessDuration} دقيقة`;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
        
        // استدعاء الدالة الأصلية
        if (window.originalStartIqamahCountdownFinal) {
            return window.originalStartIqamahCountdownFinal.call(this, prayerName, arabicName);
        }
    };
    
    console.log('✅ تم إعادة تعريف دالة العد التنازلي للإقامة نهائياً');
}

// إصلاح زر حفظ مدة التعتيم
function fixFinalSaveButton() {
    console.log('🔧 إصلاح نهائي لزر حفظ مدة التعتيم...');
    
    // البحث عن الزر بطرق متعددة
    let saveBtn = document.getElementById('save-darkness-times');
    if (!saveBtn) {
        saveBtn = document.querySelector('button[onclick*="saveDarknessTimes"]');
    }
    if (!saveBtn) {
        saveBtn = document.querySelector('button[onclick*="darkness"]');
    }
    
    if (saveBtn) {
        // إزالة جميع المستمعات السابقة
        const newSaveBtn = saveBtn.cloneNode(true);
        saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);
        
        // إضافة مستمع جديد
        newSaveBtn.addEventListener('click', function() {
            console.log('💾 حفظ نهائي لمدة التعتيم...');
            
            const darknessTimes = {};
            const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
            
            prayers.forEach(prayer => {
                // البحث في جميع الحقول الممكنة
                const possibleIds = [
                    `${prayer}-darkness`,
                    `${prayer}-darkness-duration`,
                    `${prayer}_darkness`
                ];
                
                let value = 10; // القيمة الافتراضية
                
                for (const id of possibleIds) {
                    const input = document.getElementById(id);
                    if (input && input.value) {
                        value = parseInt(input.value) || 10;
                        console.log(`📝 قراءة ${prayer} من ${id}: ${value} دقيقة`);
                        break;
                    }
                }
                
                darknessTimes[prayer] = value;
            });
            
            // حفظ في localStorage
            localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
            
            // تحديث المتغير العالمي
            window.darknessTimes = darknessTimes;
            
            console.log('✅ تم حفظ مدة التعتيم نهائياً:', darknessTimes);
            
            // إشعار مرئي
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #4CAF50;
                color: white;
                padding: 20px 40px;
                border-radius: 10px;
                z-index: 10000;
                font-weight: bold;
                font-size: 18px;
                text-align: center;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            `;
            notification.innerHTML = `
                <div>✅ تم حفظ مدة التعتيم بنجاح!</div>
                <div style="margin-top: 10px; font-size: 14px;">
                    ${Object.keys(darknessTimes).map(p => `${p}: ${darknessTimes[p]} دقيقة`).join('<br>')}
                </div>
            `;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        });
        
        console.log('✅ تم إصلاح زر الحفظ نهائياً');
    } else {
        console.warn('⚠️ لم يتم العثور على زر حفظ مدة التعتيم');
    }
}

// تحميل مدة التعتيم المحفوظة
function loadFinalDarknessTimes() {
    console.log('📁 تحميل نهائي لمدة التعتيم المحفوظة...');
    
    try {
        const savedDarkness = localStorage.getItem('darknessTimes');
        let darknessTimes;
        
        if (savedDarkness) {
            darknessTimes = JSON.parse(savedDarkness);
            console.log('✅ تم تحميل مدة التعتيم من localStorage:', darknessTimes);
        } else {
            darknessTimes = {
                fajr: 10,
                dhuhr: 10,
                asr: 10,
                maghrib: 10,
                isha: 10
            };
            console.log('🔧 استخدام مدة التعتيم الافتراضية');
        }
        
        // تحديث المتغير العالمي
        window.darknessTimes = darknessTimes;
        
        // تحديث جميع حقول الإدخال الممكنة
        Object.keys(darknessTimes).forEach(prayer => {
            const possibleIds = [
                `${prayer}-darkness`,
                `${prayer}-darkness-duration`,
                `${prayer}_darkness`
            ];
            
            possibleIds.forEach(id => {
                const input = document.getElementById(id);
                if (input) {
                    input.value = darknessTimes[prayer];
                    console.log(`📝 تحديث حقل ${id}: ${darknessTimes[prayer]}`);
                }
            });
        });
        
        console.log('✅ تم تحميل وتطبيق مدة التعتيم نهائياً');
        
    } catch (error) {
        console.error('❌ خطأ في تحميل مدة التعتيم:', error);
        
        // قيم افتراضية في حالة الخطأ
        window.darknessTimes = {
            fajr: 10,
            dhuhr: 10,
            asr: 10,
            maghrib: 10,
            isha: 10
        };
    }
}

// دالة اختبار شاملة
function testFinalDarkness() {
    console.log('🧪 اختبار نهائي لنظام التعتيم...');
    
    const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    
    console.log('📊 حالة النظام:');
    console.log('- localStorage:', localStorage.getItem('darknessTimes'));
    console.log('- window.darknessTimes:', window.darknessTimes);
    
    prayers.forEach(prayer => {
        const duration = getFinalDarknessDuration(prayer);
        console.log(`${prayer}: ${duration} دقيقة`);
        
        // فحص الحقول
        const possibleIds = [`${prayer}-darkness`, `${prayer}-darkness-duration`];
        possibleIds.forEach(id => {
            const input = document.getElementById(id);
            if (input) {
                console.log(`  حقل ${id}: ${input.value || 'فارغ'}`);
            }
        });
    });
}

// تشغيل جميع الإصلاحات النهائية
function runFinalDarknessFixes() {
    console.log('🚀 تشغيل الإصلاحات النهائية لمدة التعتيم...');
    
    loadFinalDarknessTimes();
    overrideFinalIqamahCountdown();
    fixFinalSaveButton();
    
    console.log('✅ تم تطبيق جميع الإصلاحات النهائية لمدة التعتيم');
    
    // اختبار بعد ثانية
    setTimeout(testFinalDarkness, 1000);
    
    // إشعار نهائي
    const finalNotification = document.createElement('div');
    finalNotification.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #2196F3;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-weight: bold;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    finalNotification.textContent = '🌙 تم إصلاح نظام التعتيم نهائياً';
    document.body.appendChild(finalNotification);
    
    setTimeout(() => {
        finalNotification.remove();
    }, 5000);
}

// إضافة اختصار للاختبار
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'T') {
        testFinalDarkness();
    }
});

// تشغيل الإصلاحات النهائية
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(runFinalDarknessFixes, 4000);
    });
} else {
    setTimeout(runFinalDarknessFixes, 4000);
}

console.log('✅ تم تحميل الإصلاح النهائي لمدة التعتيم - اضغط Ctrl+Shift+T للاختبار');
