// إصلاح سريع ومباشر للمشاكل
console.log('⚡ بدء الإصلاح السريع...');

// إيقاف جميع المؤقتات
for (let i = 1; i < 99999; i++) {
    clearInterval(i);
}

// إصلاح الساعة الرقمية فوراً
function quickFixClock() {
    const clock = document.querySelector('.digital-clock');
    if (clock) {
        function updateTime() {
            const now = new Date();
            const time = now.toLocaleTimeString('ar-SA', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            clock.textContent = time;
            clock.style.cssText = `
                color: #40E0D0 !important;
                font-size: 2.2em !important;
                font-weight: bold !important;
                font-family: Arial, sans-serif !important;
                background: rgba(0,0,0,0.8) !important;
                padding: 15px 25px !important;
                border-radius: 15px !important;
                border: 2px solid #40E0D0 !important;
                text-align: center !important;
                margin: 10px auto !important;
                display: block !important;
                width: fit-content !important;
            `;
        }
        updateTime();
        setInterval(updateTime, 1000);
        console.log('✅ تم إصلاح الساعة');
    }
}

// إصلاح الخلفيات فوراً
function quickFixBackground() {
    const bgSelect = document.getElementById('backgroundSelect');
    if (bgSelect) {
        bgSelect.onchange = function() {
            const bg = this.value;
            if (bg) {
                document.body.style.backgroundImage = `url('${bg}')`;
                document.body.style.backgroundSize = 'cover';
                document.body.style.backgroundPosition = 'center';
                document.body.style.backgroundRepeat = 'no-repeat';
                localStorage.setItem('selectedBackground', bg);
                console.log('✅ تم تغيير الخلفية:', bg);
                
                // إشعار مرئي
                const msg = document.createElement('div');
                msg.textContent = 'تم تغيير الخلفية';
                msg.style.cssText = `
                    position: fixed; top: 20px; right: 20px;
                    background: #4CAF50; color: white;
                    padding: 10px 20px; border-radius: 5px;
                    z-index: 10000; font-weight: bold;
                `;
                document.body.appendChild(msg);
                setTimeout(() => msg.remove(), 2000);
            }
        };
        
        // تحميل الخلفية المحفوظة
        const saved = localStorage.getItem('selectedBackground');
        if (saved) {
            bgSelect.value = saved;
            document.body.style.backgroundImage = `url('${saved}')`;
            document.body.style.backgroundSize = 'cover';
            document.body.style.backgroundPosition = 'center';
            document.body.style.backgroundRepeat = 'no-repeat';
        }
        console.log('✅ تم إصلاح الخلفيات');
    }
}

// إصلاح المدن فوراً
function quickFixCities() {
    const citySelect = document.getElementById('selected-city') || document.getElementById('city-select');
    if (citySelect) {
        citySelect.onchange = function() {
            const city = this.value;
            localStorage.setItem('selectedCity', city);
            console.log('✅ تم تغيير المدينة:', city);
            
            // إشعار مرئي
            const msg = document.createElement('div');
            msg.textContent = `تم تغيير المدينة إلى ${city}`;
            msg.style.cssText = `
                position: fixed; top: 60px; right: 20px;
                background: #2196F3; color: white;
                padding: 10px 20px; border-radius: 5px;
                z-index: 10000; font-weight: bold;
            `;
            document.body.appendChild(msg);
            setTimeout(() => msg.remove(), 2000);
            
            // تحديث المواقيت
            quickFixPrayerTimes();
        };
        
        // تحميل المدينة المحفوظة
        const saved = localStorage.getItem('selectedCity');
        if (saved) {
            citySelect.value = saved;
        }
        console.log('✅ تم إصلاح المدن');
    }
}

// إصلاح المواقيت فوراً
function quickFixPrayerTimes() {
    const times = {
        fajr: '05:15',
        sunrise: '06:45', 
        dhuhr: '12:30',
        asr: '15:45',
        maghrib: '18:15',
        isha: '19:45'
    };
    
    // تحديث عناصر المواقيت
    Object.keys(times).forEach(prayer => {
        const elements = document.querySelectorAll(`#${prayer}-time, .${prayer}-time, [data-prayer="${prayer}"]`);
        elements.forEach(el => {
            if (el) {
                el.textContent = times[prayer];
                el.style.color = '#40E0D0';
                el.style.fontWeight = 'bold';
            }
        });
    });
    
    console.log('✅ تم إصلاح المواقيت');
}

// إصلاح التعديل اليدوي فوراً
function quickFixManualEdit() {
    const saveBtn = document.getElementById('save-manual-times');
    const resetBtn = document.getElementById('reset-manual-times');
    
    if (saveBtn) {
        saveBtn.onclick = function() {
            const prayers = ['fajr', 'sunrise', 'dhuhr', 'asr', 'maghrib', 'isha'];
            const manualTimes = {};
            
            prayers.forEach(prayer => {
                const input = document.getElementById(`manual-${prayer}`);
                if (input && input.value) {
                    manualTimes[prayer] = input.value;
                }
            });
            
            if (Object.keys(manualTimes).length > 0) {
                localStorage.setItem('manualPrayerTimes', JSON.stringify(manualTimes));
                
                // تطبيق المواقيت
                Object.keys(manualTimes).forEach(prayer => {
                    const elements = document.querySelectorAll(`#${prayer}-time, .${prayer}-time`);
                    elements.forEach(el => {
                        if (el) el.textContent = manualTimes[prayer];
                    });
                });
                
                // إشعار
                const msg = document.createElement('div');
                msg.textContent = 'تم حفظ المواقيت المعدلة';
                msg.style.cssText = `
                    position: fixed; top: 100px; right: 20px;
                    background: #4CAF50; color: white;
                    padding: 10px 20px; border-radius: 5px;
                    z-index: 10000; font-weight: bold;
                `;
                document.body.appendChild(msg);
                setTimeout(() => msg.remove(), 2000);
                
                console.log('✅ تم حفظ المواقيت المعدلة');
            }
        };
    }
    
    if (resetBtn) {
        resetBtn.onclick = function() {
            if (confirm('هل تريد إعادة ضبط المواقيت؟')) {
                localStorage.removeItem('manualPrayerTimes');
                quickFixPrayerTimes();
                console.log('✅ تم إعادة ضبط المواقيت');
            }
        };
    }
    
    console.log('✅ تم إصلاح التعديل اليدوي');
}

// تشغيل جميع الإصلاحات
setTimeout(() => {
    quickFixClock();
    quickFixBackground();
    quickFixCities();
    quickFixPrayerTimes();
    quickFixManualEdit();
    
    console.log('🎉 تم تطبيق الإصلاح السريع بنجاح!');
    
    // إشعار نهائي
    const finalMsg = document.createElement('div');
    finalMsg.textContent = '🎉 تم إصلاح جميع المشاكل!';
    finalMsg.style.cssText = `
        position: fixed; top: 50%; left: 50%;
        transform: translate(-50%, -50%);
        background: #4CAF50; color: white;
        padding: 20px 40px; border-radius: 10px;
        z-index: 10000; font-weight: bold;
        font-size: 18px; text-align: center;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    `;
    document.body.appendChild(finalMsg);
    setTimeout(() => finalMsg.remove(), 3000);
    
}, 500);

console.log('✅ تم تحميل الإصلاح السريع');
