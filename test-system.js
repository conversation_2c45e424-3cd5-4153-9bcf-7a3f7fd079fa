// ملف اختبار النظام
console.log('🧪 بدء اختبار النظام...');

// دالة اختبار مدة الإقامة والتعتيم
function testSystem() {
    console.log('🔍 اختبار شامل للنظام...');
    
    // اختبار مدة الإقامة
    console.log('📊 اختبار مدة الإقامة:');
    const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    
    prayers.forEach(prayer => {
        const input = document.getElementById(`${prayer}-iqama-duration`);
        if (input) {
            console.log(`  ${prayer}: ${input.value || 'فارغ'} (حقل موجود)`);
        } else {
            console.log(`  ${prayer}: حقل غير موجود`);
        }
    });
    
    // اختبار مدة التعتيم
    console.log('📊 اختبار مدة التعتيم:');
    prayers.forEach(prayer => {
        const input1 = document.getElementById(`${prayer}-darkness`);
        const input2 = document.getElementById(`${prayer}-darkness-duration`);
        
        if (input1) {
            console.log(`  ${prayer}: ${input1.value || 'فارغ'} (حقل ${prayer}-darkness موجود)`);
        } else if (input2) {
            console.log(`  ${prayer}: ${input2.value || 'فارغ'} (حقل ${prayer}-darkness-duration موجود)`);
        } else {
            console.log(`  ${prayer}: لا توجد حقول`);
        }
    });
    
    // اختبار localStorage
    console.log('📊 اختبار التخزين المحلي:');
    console.log('  iqama_durations:', localStorage.getItem('iqama_durations'));
    console.log('  darknessTimes:', localStorage.getItem('darknessTimes'));
    
    // اختبار المتغيرات العالمية
    console.log('📊 اختبار المتغيرات العالمية:');
    console.log('  window.iqamahTimes:', window.iqamahTimes);
    console.log('  window.darknessTimes:', window.darknessTimes);
    
    // اختبار الأزرار
    console.log('📊 اختبار الأزرار:');
    const saveIqamahBtn = document.getElementById('save-iqamah-times');
    const saveDarknessBtn = document.getElementById('save-darkness-times');
    
    console.log('  زر حفظ الإقامة:', saveIqamahBtn ? 'موجود' : 'غير موجود');
    console.log('  زر حفظ التعتيم:', saveDarknessBtn ? 'موجود' : 'غير موجود');
    
    // اختبار الدوال
    console.log('📊 اختبار الدوال:');
    console.log('  startIqamahCountdown:', typeof window.startIqamahCountdown);
    
    // إنشاء تقرير مرئي
    createTestReport();
}

// دالة إنشاء تقرير مرئي
function createTestReport() {
    // إزالة التقرير السابق
    const existingReport = document.getElementById('test-report');
    if (existingReport) existingReport.remove();
    
    const report = document.createElement('div');
    report.id = 'test-report';
    report.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 20px;
        border-radius: 10px;
        z-index: 10000;
        max-width: 400px;
        max-height: 80vh;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        border: 2px solid #4CAF50;
    `;
    
    let reportHTML = '<h3 style="color: #4CAF50; margin-top: 0;">🧪 تقرير اختبار النظام</h3>';
    
    // فحص حقول الإقامة
    reportHTML += '<h4 style="color: #40E0D0;">⏰ حقول مدة الإقامة:</h4>';
    const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    prayers.forEach(prayer => {
        const input = document.getElementById(`${prayer}-iqama-duration`);
        const status = input ? `✅ ${input.value || 'فارغ'}` : '❌ غير موجود';
        reportHTML += `<p>${prayer}: ${status}</p>`;
    });
    
    // فحص حقول التعتيم
    reportHTML += '<h4 style="color: #40E0D0;">🌙 حقول مدة التعتيم:</h4>';
    prayers.forEach(prayer => {
        const input1 = document.getElementById(`${prayer}-darkness`);
        const input2 = document.getElementById(`${prayer}-darkness-duration`);
        
        if (input1) {
            reportHTML += `<p>${prayer}: ✅ ${input1.value || 'فارغ'}</p>`;
        } else if (input2) {
            reportHTML += `<p>${prayer}: ✅ ${input2.value || 'فارغ'}</p>`;
        } else {
            reportHTML += `<p>${prayer}: ❌ غير موجود</p>`;
        }
    });
    
    // فحص الأزرار
    reportHTML += '<h4 style="color: #40E0D0;">🔘 الأزرار:</h4>';
    const saveIqamahBtn = document.getElementById('save-iqamah-times');
    const saveDarknessBtn = document.getElementById('save-darkness-times');
    
    reportHTML += `<p>حفظ الإقامة: ${saveIqamahBtn ? '✅ موجود' : '❌ غير موجود'}</p>`;
    reportHTML += `<p>حفظ التعتيم: ${saveDarknessBtn ? '✅ موجود' : '❌ غير موجود'}</p>`;
    
    // فحص التخزين المحلي
    reportHTML += '<h4 style="color: #40E0D0;">💾 التخزين المحلي:</h4>';
    const savedIqamah = localStorage.getItem('iqama_durations');
    const savedDarkness = localStorage.getItem('darknessTimes');
    
    reportHTML += `<p>الإقامة: ${savedIqamah ? '✅ محفوظ' : '❌ غير محفوظ'}</p>`;
    reportHTML += `<p>التعتيم: ${savedDarkness ? '✅ محفوظ' : '❌ غير محفوظ'}</p>`;
    
    // زر اختبار سريع
    reportHTML += '<hr style="border-color: #40E0D0;">';
    reportHTML += '<button onclick="testQuickIqamah()" style="background: #4CAF50; color: white; border: none; padding: 10px; border-radius: 5px; margin: 5px; cursor: pointer;">اختبار الإقامة السريع</button>';
    reportHTML += '<button onclick="this.parentElement.remove()" style="background: #f44336; color: white; border: none; padding: 10px; border-radius: 5px; margin: 5px; cursor: pointer;">إغلاق التقرير</button>';
    
    report.innerHTML = reportHTML;
    document.body.appendChild(report);
}

// دالة اختبار سريع للإقامة
function testQuickIqamah() {
    console.log('🧪 اختبار سريع للإقامة...');
    
    if (typeof window.startIqamahCountdown === 'function') {
        // اختبار مع صلاة العصر
        window.startIqamahCountdown('asr', 'العصر');
        console.log('✅ تم تشغيل اختبار الإقامة');
    } else {
        console.error('❌ دالة startIqamahCountdown غير موجودة');
        alert('❌ دالة startIqamahCountdown غير موجودة');
    }
}

// إضافة اختصارات لوحة المفاتيح
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'T') {
        testSystem();
    }
    
    if (e.ctrlKey && e.shiftKey && e.key === 'Q') {
        testQuickIqamah();
    }
});

// تشغيل الاختبار تلقائياً بعد التحميل
setTimeout(() => {
    console.log('🔍 تشغيل اختبار تلقائي للنظام...');
    testSystem();
}, 6000);

console.log('✅ تم تحميل ملف اختبار النظام');
console.log('📝 اضغط Ctrl+Shift+T لتشغيل الاختبار');
console.log('📝 اضغط Ctrl+Shift+Q لاختبار الإقامة السريع');
