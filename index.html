<!DOCTYPE html>
<html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="theme-color" content="#4a3b3b">
        <link rel="manifest" href="./manifest.json">
        <title>ساعة المسجد - مواقيت الصلاة</title>

        <!-- المكتبات المطلوبة -->
        <script src="node_modules/moment/min/moment.min.js"></script>
        <script src="node_modules/moment-hijri/moment-hijri.js"></script>

        <!-- مكتبة حساب مواقيت الصلاة -->
        <script src="js/PrayTimes.js"></script>

        <!-- ملفات الساعة ومواقيت الصلاة الجديدة -->
        <script src="prayer-times-new.js"></script>
        <script src="clock-new.js"></script>

        <!-- نظام مواقيت الصلاة الدقيق -->
        <script src="prayer-times-accurate.js"></script>
        <script src="prayer-manager.js"></script>
        <script src="prayer-darkness-single.js"></script>
        <script src="prayer-integration.js"></script>

        <!-- نظام مواقيت الصلاة المحسن مع التحديث المستمر -->
        <script src="prayer-times-manager.js"></script>
        <script src="prayer-times-init.js"></script>

        <!-- عناصر الصوت -->
        <audio id="adhan-audio" preload="auto"></audio>

        <!-- أنماط إضافية -->
        <style>
            /* تم تعديل أنماط زر تحديث مواقيت الصلاة ليكون بجانب زر الإعدادات */
            #main-update-prayer-times {
                font-size: 20px;
                transition: all 0.3s ease;
            }

            #main-update-prayer-times:hover {
                transform: scale(1.1);
                box-shadow: 0 0 10px rgba(212, 175, 55, 0.7);
            }
        </style>

        <!-- تهيئة مكتبة PrayTimes.js -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // التحقق من وجود مكتبة PrayTimes.js
                if (typeof prayTimes !== 'undefined') {
                    // تعيين المتغير العالمي
                    window.prayTimes = prayTimes;
                    console.log('تم تهيئة مكتبة PrayTimes.js بنجاح');

                    // تحديث مواقيت الصلاة باستخدام المكتبة
                    setTimeout(function() {
                        if (typeof getPrayerTimes === 'function') {
                            getPrayerTimes();

                            // إضافة زر تحديث إجباري للمواقيت
                            const forceUpdateBtn = document.getElementById('force-update-prayer-times');
                            if (forceUpdateBtn) {
                                forceUpdateBtn.addEventListener('click', function() {
                                    // عرض إشعار
                                    if (typeof showNotification === 'function') {
                                        showNotification('جاري التحديث الإجباري لمواقيت الصلاة...', 'info');
                                    } else {
                                        alert('جاري التحديث الإجباري لمواقيت الصلاة...');
                                    }

                                    // إعادة تهيئة مكتبة PrayTimes.js
                                    window.prayTimes = prayTimes;

                                    // الحصول على القيم المحددة
                                    const selectedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                                    const selectedMethod = localStorage.getItem('calculationMethod') || 'MWL';
                                    const selectedJuristicMethod = localStorage.getItem('juristicMethod') || 'Shafi';

                                    // تعيين طريقة الحساب
                                    window.prayTimes.setMethod(selectedMethod);

                                    // تعيين المذهب (للعصر)
                                    const juristicMethodValue = selectedJuristicMethod === 'Hanafi' ? 1 : 0;
                                    window.prayTimes.adjust({asr: juristicMethodValue});

                                    // إعادة حساب مواقيت الصلاة
                                    getPrayerTimes();

                                    // عرض إشعار
                                    if (typeof showNotification === 'function') {
                                        showNotification('تم التحديث الإجباري لمواقيت الصلاة بنجاح', 'success');
                                    } else {
                                        alert('تم التحديث الإجباري لمواقيت الصلاة بنجاح');
                                    }
                                });
                            }

                            // تفعيل الأذان
                            const enableAdhanCheckbox = document.getElementById('enable-adhan');
                            if (enableAdhanCheckbox) {
                                // تحميل الإعدادات المحفوظة
                                const savedSettings = JSON.parse(localStorage.getItem('adhanSettings') || '{"enabled":true,"volume":1}');
                                enableAdhanCheckbox.checked = savedSettings.enabled;

                                // إضافة مستمع للتغيير
                                enableAdhanCheckbox.addEventListener('change', function() {
                                    const settings = {
                                        enabled: this.checked,
                                        volume: parseFloat(document.getElementById('adhan-volume')?.value || 1)
                                    };

                                    // حفظ الإعدادات
                                    localStorage.setItem('adhanSettings', JSON.stringify(settings));

                                    // عرض إشعار
                                    if (typeof showNotification === 'function') {
                                        showNotification(`تم ${this.checked ? 'تفعيل' : 'تعطيل'} الأذان`, 'success');
                                    } else {
                                        alert(`تم ${this.checked ? 'تفعيل' : 'تعطيل'} الأذان`);
                                    }
                                });
                            }
                        }
                    }, 1000);
                } else {
                    console.error('مكتبة PrayTimes.js غير متوفرة');
                }
            });
        </script>

        <style>
            /* ... باقي الأنماط ... */
        :root {
            --gold-color: #71d3ee;
            --dark-pink: #4a3b3b;
            --light-gray: #faf5f5;
            --dark-gray: #333333;
            --text-cِor: white;
            --text-size: 24px;
            --second-hand-color: #f20b0b;
            --minute-hand-color: #000000;
            --hour-hand-color: #8B4513;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #fffbfb;
            background-size: cover;
            background-position: center;
            direction: rtl;
            display: flex;
            height: 100vh;
            transform-origin: top center;
        }

        .vertical-panel {
            width: 5cm;
            height: 100vh;
            background-color: var(--dark-pink);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px;
            box-sizing: border-box;
            position: fixed;
            top: 0;
            right: 0;
            box-shadow: -2px 0 5px #fcf9f9;
            color: var(--gold-color);
            justify-content: flex-start;
            padding-top: 5px;
            overflow-y: auto;
        }

        .settings-btn {
            position: fixed;
            left: 15px;
            top: 15px;
            color: var(--gold-color);
            cursor: pointer;
            font-size: 1.5em;
            background: none;
            border: none;
            z-index: 1000;
        }

        .settings-btn:hover {
            transform: rotate(45deg);
            transition: transform 0.3s;
        }

        .settings-menu {
            display: none;
            position: fixed;
            left: 15px;
            top: 60px;
            width: 250px; /* زيادة العرض قليلاً */
            background-color: rgba(0, 0, 0, 0.9);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            padding: 15px;
            flex-direction: column;
            gap: 8px;
            color: #D4AF37;
            z-index: 999;
            font-size: 0.9em;
            max-height: 80vh;
            overflow-y: auto;
        }

        .settings-menu.active {
            display: flex;
        }

        .settings-menu label {
            color: #D4AF37;
            font-weight: bold;
            margin-top: 5px;
            display: block;
        }

        .settings-menu select,
        .settings-menu input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 4px 0;
            background-color: #000;
            color: #D4AF37;
            border: 1px solid #D4AF37;
            border-radius: 4px;
            cursor: pointer;
            outline: none;
        }

        .mosque-settings {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #D4AF37;
            border-radius: 4px;
        }

        .font-size-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin: 10px 0;
        }

        .font-size-btn {
            background-color: var(--dark-pink);
            color: var(--gold-color);
            border: 2px solid var(--gold-color);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .font-size-btn:hover {
            background-color: var(--gold-color);
            color: var(--dark-pink);
        }

        .color-control {
            margin: 10px 0;
        }

        .color-control input[type="color"] {
            width: 50px;
            height: 30px;
            padding: 0;
            border: 2px solid #D4AF37;
            border-radius: 4px;
            background: none;
            cursor: pointer;
        }

        .settings-menu select:hover {
            background-color: #1a1a1a;
        }

        .settings-menu select:focus {
            border-color: #fff;
        }

        .settings-menu button {
            background-color: #D4AF37;
            color: #000;
            border: none;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 5px;
            width: 100%;
        }

        .settings-menu button:hover {
            background-color: #fff;
            color: #000;
        }

        /* تنسيق شريط التمرير */
        .settings-menu::-webkit-scrollbar {
            width: 8px;
        }

        .settings-menu::-webkit-scrollbar-track {
            background: #000;
            border-radius: 4px;
        }

        .settings-menu::-webkit-scrollbar-thumb {
            background-color: #D4AF37;
            border-radius: 4px;
        }

        .settings-menu::-webkit-scrollbar-thumb:hover {
            background-color: #fff;
        }

        /* تنسيق Firefox */
        .settings-menu {
            scrollbar-width: thin;
            scrollbar-color: #D4AF37 #000;
        }

        .text-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            width: 80%;
            max-width: 800px;
            z-index: 9999;
            direction: rtl;
        }

        .text-overlay {
            color: var(--gold-color);
            font-size: var(--text-size);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            line-height: 1.5;
        }

        .counter {
            color: var(--gold-color);
            font-size: calc(var(--text-size) * 0.7);
            margin-top: 10px;
        }

        .note-overlay {
            color: var(--gold-color);
            font-size: calc(var(--text-size) * 0.7);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            transition: opacity 0.5s ease;
            opacity: 0.8;
        }

        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            margin-top: 10px;
        }

        .weather-container {
            width: 100%;
            max-width: 120px;
            text-align: center;
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 12px;
            padding: 8px;
            margin: 5px auto 15px auto;
            border: 2px solid var(--gold-color);
            box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }

        .dates {
            color: var(--gold-color);
            text-align: center;
            margin: 10px 0;
            padding: 5px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            width: 85%;
        }

        .element-divider {
            width: 85%;
            margin: 15px auto;
            border-bottom: 2px solid #40E0D0;
            box-shadow: 0 0 5px rgba(64, 224, 208, 0.6);
            position: relative;
            left: 0;
            right: 0;
            transform: none;
        }

        .analog-clock {
            width: 140px;
            height: 140px;
            border: 4px solid var(--gold-color);
            border-radius: 50%;
            position: relative;
            background: rgb(252, 250, 250);
            margin: 5px 0;
        }

        .analog-clock .hand {
            position: absolute;
            bottom: 50%;
            left: 50%;
            transform-origin: bottom;
        }

        .hour-hand {
            width: 4px;
            height: 30%;
            background: var(--hour-hand-color);
        }

        .minute-hand {
            width: 3px;
            height: 40%;
            background: var(--minute-hand-color);
        }

        .second-hand {
            width: 2px;
            height: 45%;
            background: var(--second-hand-color);
        }

        .digital-clock {
            color: var(--gold-color);
            font-size: 2em;
            text-align: center;
            margin: 10px auto 5px auto;
            font-weight: bold;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 5px 15px;
            border-radius: 15px;
            border: 2px solid var(--gold-color);
            box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
            width: 85%;
            max-width: 200px;
        }

        .countdown-circle {
            margin: 20px auto 10px auto;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background-color: white;
            border: 4px solid var(--gold-color);
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            aspect-ratio: 1 / 1;
            box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
        }

        /* تنسيق خاص للعد التنازلي داخل عنصر الإقامة */
        .iqamah-countdown .countdown-circle {
            width: 100%;
            height: auto;
            background-color: transparent;
            border: none;
            box-shadow: none;
            margin: 0;
        }

        .iqamah-countdown .countdown-wrapper {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .prayer-times {
            width: calc(100% - 5cm);
            height: 3cm;
            background-color: var(--dark-pink);
            color: var(--gold-color);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 10px 0;
            position: fixed;
            bottom: 0;
            left: 0;
            z-index: 5;
            box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);
        }

        .prayer-time {
            text-align: center;
            padding: 10px;
        }

        .prayer-name {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #40E0D0;
        }

        .prayer-hour {
            font-size: 1.7em;
            margin-top: 10px;
            color: #40E0D0;
        }

        .number {
            position: absolute;
            font-size: 12px;
            color: var(--dark-gray);
            text-align: center;
            width: 20px;
            height: 20px;
            line-height: 20px;
            transform: translate(-50%, -50%);
        }

        .weather-display {
            color: #40E0D0;
            font-size: 0.9em;
        }

        .weather-display img {
            display: block;
            margin: 0 auto 5px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 50%;
            box-shadow: 0 0 5px rgba(64, 224, 208, 0.5);
            width: 50px;
            height: 50px;
        }

        .weather-display .temperature {
            font-size: 1.3em;
            font-weight: bold;
            margin: 5px 0;
            text-shadow: 0 0 5px rgba(64, 224, 208, 0.7);
        }

        .weather-display .description {
            margin-top: 3px;
            font-size: 0.85em;
            padding: 0 3px;
            text-transform: capitalize;
            line-height: 1.2;
        }

        .day-display {
            position: fixed;
            top: 20px;
            right: calc(5cm + 20px);
            font-family: 'Arial', sans-serif;
            font-size: 32px;
            font-weight: bold;
            color: white;
            background-color: var(--dark-pink);
            padding: 15px 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 100000;
            direction: rtl;
            text-align: center;
            border: 3px solid var(--gold-color);
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            pointer-events: none;
        }

        .location-settings {
            width: 100%;
            margin-bottom: 15px;
        }

        .settings-select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            background-color: rgba(0, 0, 0, 0.8);
            color: var(--gold-color);
            border: 1px solid var(--gold-color);
            border-radius: 4px;
            cursor: pointer;
        }

        .settings-select:hover {
            background-color: rgba(0, 0, 0, 0.9);
        }

        .settings-select option {
            background-color: rgba(0, 0, 0, 0.9);
            color: var(--gold-color);
            padding: 8px;
        }

        .settings-menu label {
            display: block;
            margin-top: 10px;
            color: var(--gold-color);
            font-weight: bold;
        }

        /* تنسيق مواقيت الصلوات */
        .prayer-times .prayer-time {
            color: white;
        }

        .prayer-times .prayer-time .prayer-name {
            font-size: 1.4em;
            font-weight: bold;
        }

        .prayer-times .prayer-time .prayer-hour {
            font-size: 1.3em;
        }

        .prayer-times .prayer-time .countdown-value {
            font-size: 1.3em;
        }

        /* إزالة التنسيقات المكررة وإخفاء العناصر التي يجب أن تكون مخفية */
        .prayer-box {
            display: none;
        }

        /* إخفاء العناصر التي يجب أن تكون في الإعدادات فقط */
        .vertical-panel .settings-menu {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
        }

        /* إظهار قائمة الإعدادات فقط عند النقر على زر الإعدادات */
        .vertical-panel .settings-btn:focus + .settings-menu,
        .vertical-panel .settings-menu:hover,
        .vertical-panel .settings-menu:focus-within {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            pointer-events: auto !important;
        }

        /* إخفاء قسم الإعدادات بشكل كامل */
        .settings-section, .settings-menu-content {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
        }

        /* إظهار قسم الإعدادات فقط داخل قائمة الإعدادات */
        .settings-menu .settings-section,
        .settings-menu .settings-menu-content {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            pointer-events: auto !important;
        }

        .day-display {
            position: fixed;
            top: 20px;
            right: calc(5cm + 20px);
            font-family: 'Arial', sans-serif;
            font-size: 32px;
            font-weight: bold;
            color: white;
            background-color: var(--dark-pink);
            padding: 15px 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 100000;
            direction: rtl;
            text-align: center;
            border: 3px solid var(--gold-color);
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            pointer-events: none;
        }

        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            margin-top: 5px;
        }

        .weather-container {
            width: 100%;
            max-width: 120px;
            text-align: center;
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 12px;
            padding: 8px;
            margin: 5px auto 15px auto;
            border: 2px solid var(--gold-color);
            box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }

        .weather-display {
            color: #40E0D0;
            font-size: 0.9em;
        }

        .weather-display img {
            display: block;
            margin: 0 auto 5px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 50%;
            box-shadow: 0 0 5px rgba(64, 224, 208, 0.5);
            width: 50px;
            height: 50px;
        }

        .weather-display .temperature {
            font-size: 1.3em;
            font-weight: bold;
            margin: 5px 0;
            text-shadow: 0 0 5px rgba(64, 224, 208, 0.7);
        }

        .weather-display .description {
            margin-top: 3px;
            font-size: 0.85em;
            padding: 0 3px;
            text-transform: capitalize;
            line-height: 1.2;
        }

        .dates {
            color: var(--gold-color);
            text-align: center;
            margin: 10px 0 20px 0;
            padding: 5px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            width: 85%;
        }

        .gregorian-date,
        .hijri-date {
            margin: 2px 0;
            font-size: 1.1em;
        }

        .analog-clock {
            width: 140px;
            height: 140px;
            border: 4px solid var(--gold-color);
            border-radius: 50%;
            position: relative;
            background: rgb(252, 250, 250);
            margin: 10px 0 15px 0;
        }

        .digital-clock {
            color: var(--gold-color);
            font-size: 2em;
            text-align: center;
            margin: 10px auto;
            font-weight: bold;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 5px 15px;
            border-radius: 15px;
            border: 2px solid var(--gold-color);
            box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
            width: 85%;
            max-width: 200px;
        }

        .countdown-circle {
            margin: 20px auto 10px auto;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background-color: white;
            border: 4px solid var(--gold-color);
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            aspect-ratio: 1 / 1;
            box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
        }

        .countdown-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            text-align: center;
            color: var(--gold-color);
            position: relative;
            z-index: 2;
        }

        .countdown-time {
            font-size: 2.2em;
            font-weight: 900;
            margin-bottom: 5px;
            color: #ff0000;
            text-shadow: 0 0 3px rgba(255, 0, 0, 0.5);
            line-height: 1;
        }

        .next-prayer-text {
            font-size: 1.1em;
            font-weight: 700;
            margin: 5px 0;
            color: #000000;
            line-height: 1.2;
        }

        /* إضافة أنماط جديدة للأزرار */
        .screen-size-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-top: 8px;
        }

        .screen-size-button {
            background-color: #333;
            color: #D4AF37;
            border: 1px solid #D4AF37;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
        }

        .screen-size-button.active {
            background-color: #D4AF37;
            color: #000;
        }

        .orientation-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
            width: 100%;
        }

        .orientation-button {
            padding: 8px;
            border: 1px solid #D4AF37;
            background-color: #000;
            color: #D4AF37;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .orientation-button.active {
            background-color: #D4AF37;
            color: #000;
        }

        .orientation-button:hover {
            background-color: #D4AF37;
            color: #000;
        }

        /* تعديل container للتكيف مع الاتجاه */
        .container.horizontal {
            flex-direction: row;
            justify-content: space-around;
        }

        .container.vertical {
            flex-direction: column;
            align-items: center;
        }

        /* إضافة أنماط جديدة */
        .settings-input {
            width: 100%;
            padding: 8px;
            margin: 4px 0;
            background-color: #000;
            color: #D4AF37;
            border: 1px solid #D4AF37;
            border-radius: 4px;
            outline: none;
        }

        .settings-input:focus {
            border-color: #fff;
        }

        .font-size-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 5px 0;
        }

        .font-size-btn {
            background-color: #D4AF37;
            color: #000;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .font-size-btn:hover {
            background-color: #fff;
        }

        #mosque-font-size {
            color: #D4AF37;
            font-size: 16px;
            min-width: 30px;
            text-align: center;
        }

        .mosque-name-display {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 24px;
            color: #D4AF37;
            text-align: center;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            z-index: 1000;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px 20px;
            border-radius: 10px;
            border: 2px solid #D4AF37;
            transition: all 0.3s ease;
            font-family: 'Arial', sans-serif;
            font-weight: bold;
            background-color: var(--dark-pink);
            padding: 15px 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 100000;
            direction: rtl;
            text-align: center;
            border: 3px solid var(--gold-color);
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* إضافة أنماط CSS جديدة للتحكم في حجم الشاشة */
        .screen-size-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin: 10px 0;
        }

        .screen-size-btn {
            background-color: var(--dark-pink);
            color: var(--gold-color);
            border: 2px solid var(--gold-color);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .screen-size-btn:hover {
            background-color: var(--gold-color);
            color: var(--dark-pink);
        }

        #screen-size-value {
            color: var(--gold-color);
            font-size: 16px;
            min-width: 40px;
            text-align: center;
        }

        .preset-sizes {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 5px;
            margin-top: 10px;
        }

        .preset-size-btn {
            background-color: var(--dark-pink);
            color: var(--gold-color);
            border: 1px solid var(--gold-color);
            padding: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .preset-size-btn:hover {
            background-color: var(--gold-color);
            color: var(--dark-pink);
        }

        .preset-size-btn.active {
            background-color: var(--gold-color);
            color: var(--dark-pink);
        }

        /* أنماط الإشعارات */
        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            text-align: center;
            direction: rtl;
            min-width: 250px;
            white-space: pre-line;
            max-width: 80%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .notification.success {
            background-color: #4CAF50;
            border-right: 5px solid #2E7D32;
        }

        .notification.error {
            background-color: #F44336;
            border-right: 5px solid #B71C1C;
        }

        .notification.info {
            background-color: #2196F3;
            border-right: 5px solid #0D47A1;
        }

        .notification.warning {
            background-color: #FF9800;
            border-right: 5px solid #E65100;
        }

        /* أنماط إعدادات مواقيت الصلاة */
        .prayer-times-settings {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .settings-section {
            margin-bottom: 15px;
            padding: 10px;
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 5px;
        }

        .settings-section h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--gold-color);
            font-size: 1.1em;
        }

        .settings-buttons {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 15px;
        }

        .zoom-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 5px 10px;
            border-radius: 20px;
            z-index: 100000;
        }

        .zoom-controls button {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark-pink);
            color: var(--gold-color);
            font-size: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .zoom-controls button:hover {
            background-color: var(--gold-color);
            color: var(--dark-pink);
        }

        #zoom-level {
            color: white;
            font-size: 14px;
            min-width: 50px;
            text-align: center;
        }

        /* أنماط قسم تعديل مواقيت الصلاة */
        .prayer-times-settings {
            margin: 15px 0;
            padding: 10px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
        }

        .prayer-time-inputs {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin: 10px 0;
        }

        .prayer-time-input {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .prayer-time-input label {
            color: var(--gold-color);
            font-size: 0.9em;
        }

        .prayer-time-input input {
            width: 100%;
            padding: 5px;
            background-color: rgba(0, 0, 0, 0.5);
            border: 1px solid var(--gold-color);
            border-radius: 4px;
            color: var(--gold-color);
            font-size: 0.9em;
        }

        .prayer-times-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .settings-button {
            flex: 1;
            padding: 8px;
            background-color: var(--dark-pink);
            color: var(--gold-color);
            border: 1px solid var(--gold-color);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .settings-button:hover {
            background-color: var(--gold-color);
            color: var(--dark-pink);
        }

        .iqamah-countdown {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
            background-color: rgba(0, 0, 0, 0.9);
            padding: 40px;
            border-radius: 20px;
            border: 5px solid var(--gold-color);
            text-align: center;
            box-shadow: 0 0 30px rgba(212, 175, 55, 0.7);
            min-width: 400px;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .iqamah-countdown #iqamah-label {
            font-size: 36px;
            color: var(--gold-color);
            margin-bottom: 20px;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
        }

        .iqamah-countdown #iqamah-time {
            font-size: 80px;
            color: #ff0000;
            font-weight: 900;
            margin: 20px 0;
            text-shadow: 0 0 15px rgba(255, 0, 0, 0.7);
            letter-spacing: 5px;
        }

        .iqamah-countdown #iqamah-prayer-name {
            font-size: 32px;
            color: var(--gold-color);
            font-weight: bold;
            margin-top: 20px;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
        }

        /* إضافة قسم مدة التعتيم */
        .darkness-settings {
            margin-top: 15px;
            padding: 10px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
        }

        .darkness-settings .prayer-time-inputs {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin: 10px 0;
        }

        .darkness-settings .prayer-time-input {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .darkness-settings .prayer-time-input label {
            color: var(--gold-color);
            font-size: 0.9em;
        }

        .darkness-settings .prayer-time-input input {
            width: 100%;
            padding: 5px;
            background-color: rgba(0, 0, 0, 0.5);
            border: 1px solid var(--gold-color);
            border-radius: 4px;
            color: var(--gold-color);
            font-size: 0.9em;
        }

        .darkness-settings .settings-button {
            flex: 1;
            padding: 8px;
            background-color: var(--dark-pink);
            color: var(--gold-color);
            border: 1px solid var(--gold-color);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .darkness-settings .settings-button:hover {
            background-color: var(--gold-color);
            color: var(--dark-pink);
        }

        .prayer-times-control {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-top: 10px;
        }

        .prayer-time-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            gap: 10px;
        }

        .prayer-time-row label {
            width: 80px;
            color: #fff;
        }

        .prayer-time-input {
            flex: 1;
            padding: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .save-time-btn {
            padding: 8px 15px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .save-time-btn:hover {
            background: #45a049;
        }

        .prayer-time-controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .reset-btn, .sync-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .reset-btn {
            background: #f44336;
            color: white;
        }

        .sync-btn {
            background: #2196F3;
            color: white;
        }

        .reset-btn:hover {
            background: #d32f2f;
        }

        .sync-btn:hover {
            background: #1976D2;
        }

        .edit-prayer-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            padding: 5px 10px;
            margin-top: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .edit-prayer-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .edit-prayer-modal {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 20px;
            border-radius: 10px;
            z-index: 1000;
            min-width: 300px;
        }

        .edit-prayer-modal.active {
            display: block;
        }

        .edit-prayer-modal h3 {
            color: white;
            margin-bottom: 15px;
            text-align: center;
        }

        .edit-prayer-modal input {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            color: white;
        }

        .edit-prayer-modal .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .edit-prayer-modal button {
            padding: 8px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .edit-prayer-modal .save-btn {
            background: #4CAF50;
            color: white;
        }

        .edit-prayer-modal .cancel-btn {
            background: #f44336;
            color: white;
        }

        .edit-prayer-modal .save-btn:hover {
            background: #45a049;
        }

        .edit-prayer-modal .cancel-btn:hover {
            background: #d32f2f;
        }

        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .modal-overlay.active {
            display: block;
        }

        .manual-adjustment-section {
            margin-top: 20px;
            padding: 15px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .adjustment-controls {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin: 15px 0;
        }

        .adjustment-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .adjustment-item label {
            min-width: 80px;
        }

        .adjustment-item input {
            width: 80px;
            padding: 5px;
            border-radius: 5px;
            border: 1px solid #ccc;
            background-color: rgba(255, 255, 255, 0.9);
        }

        .save-button {
            width: 100%;
            padding: 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }

        .save-button:hover {
            background-color: #45a049;
        }

        .darkness-duration-section {
            margin-top: 20px;
            padding: 15px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .darkness-controls {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin: 15px 0;
        }

        .darkness-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .darkness-item label {
            min-width: 80px;
        }

        .darkness-item input {
            width: 80px;
            padding: 5px;
            border-radius: 5px;
            border: 1px solid #ccc;
            background-color: rgba(255, 255, 255, 0.9);
        }

        #dim-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 9999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 24px;
        }

        #dim-countdown {
            font-size: 48px;
            margin-top: 20px;
        }

        #dim-prayer-name {
            font-size: 32px;
            margin-bottom: 20px;
        }

        /* أنماط قسم التعديل اليدوي لمواقيت الصلاة */
        .manual-prayer-times {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 15px;
            padding: 10px;
            background-color: rgba(128, 0, 32, 0.1);
            border-radius: 10px;
        }

        .prayer-time-edit {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .prayer-time-edit label {
            min-width: 80px;
            font-weight: bold;
            color: #800020;
        }

        .time-input {
            padding: 8px;
            border: 2px solid #D4AF37;
            border-radius: 5px;
            background-color: #fff;
            color: #800020;
            font-size: 16px;
            width: 120px;
        }

        .edit-prayer-time-btn {
            padding: 8px 15px;
            background-color: #800020;
            color: #D4AF37;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .edit-prayer-time-btn:hover {
            background-color: #D4AF37;
            color: #800020;
        }

        .settings-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }

        /* أنماط قسم إعدادات الألوان */
        .color-settings {
            margin: 15px 0;
            padding: 15px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            border: 1px solid var(--gold-color);
        }

        .color-settings h4 {
            color: var(--gold-color);
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
            font-size: 1.2em;
        }

        .color-section {
            margin-bottom: 20px;
            padding: 10px;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            border: 1px solid rgba(212, 175, 55, 0.3);
        }

        .color-section > label {
            display: block;
            color: var(--gold-color);
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1em;
        }

        .color-control {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 8px 0;
            gap: 10px;
        }

        .color-control label {
            color: var(--gold-color);
            font-size: 0.9em;
            flex: 1;
        }

        .color-control input[type="color"] {
            width: 60px;
            height: 35px;
            padding: 0;
            border: 2px solid var(--gold-color);
            border-radius: 6px;
            background: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .color-control input[type="color"]:hover {
            border-color: #fff;
            transform: scale(1.05);
        }

        .color-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .color-buttons .settings-button {
            flex: 1;
            padding: 10px;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        /* أنماط متجاوبة للهواتف المحمولة */
        @media screen and (max-width: 768px) {
            body {
                flex-direction: column !important;
                height: auto !important;
                min-height: 100vh !important;
            }

            .vertical-panel {
                width: 100% !important;
                height: auto !important;
                position: relative !important;
                top: 0 !important;
                right: 0 !important;
                flex-direction: row !important;
                justify-content: space-around !important;
                padding: 10px !important;
                order: 1 !important;
                overflow-x: auto !important;
                overflow-y: visible !important;
            }

            .prayer-times {
                width: 100% !important;
                height: auto !important;
                position: relative !important;
                bottom: auto !important;
                left: 0 !important;
                padding: 15px 5px !important;
                order: 3 !important;
                flex-wrap: wrap !important;
            }

            .prayer-time {
                flex: 1 !important;
                min-width: 80px !important;
                margin: 2px !important;
                padding: 5px !important;
            }

            .prayer-name {
                font-size: 0.9em !important;
                margin-bottom: 5px !important;
            }

            .prayer-hour {
                font-size: 0.8em !important;
                margin-top: 5px !important;
            }

            .main-content {
                margin-right: 0 !important;
                margin-bottom: 0 !important;
                width: 100% !important;
                height: auto !important;
                padding: 10px !important;
                order: 2 !important;
            }

            .container {
                margin-top: 0 !important;
                padding: 10px !important;
            }

            .analog-clock {
                width: 120px !important;
                height: 120px !important;
            }

            .digital-clock {
                font-size: 1.5em !important;
                width: 90% !important;
                padding: 8px 12px !important;
            }

            .countdown-circle {
                width: 120px !important;
                height: 120px !important;
                margin: 15px auto !important;
            }

            .weather-container {
                max-width: 100px !important;
                padding: 6px !important;
            }

            .dates {
                width: 95% !important;
                font-size: 0.9em !important;
                padding: 8px !important;
            }

            .settings-btn {
                position: fixed !important;
                left: 10px !important;
                top: 10px !important;
                z-index: 1001 !important;
                font-size: 1.2em !important;
            }

            .settings-menu {
                left: 10px !important;
                top: 50px !important;
                width: calc(100vw - 40px) !important;
                max-width: 350px !important;
                max-height: 70vh !important;
            }

            .zoom-controls {
                position: fixed !important;
                top: 10px !important;
                right: 10px !important;
                z-index: 1000 !important;
            }

            .date-display, .day-display {
                position: relative !important;
                top: auto !important;
                left: auto !important;
                text-align: center !important;
                margin: 5px 0 !important;
            }

            .text-container {
                position: relative !important;
                top: auto !important;
                left: auto !important;
                transform: none !important;
                width: 100% !important;
                max-width: 100% !important;
                padding: 10px !important;
                order: 4 !important;
            }
        }

        /* أنماط خاصة للوضع الأفقي على الهاتف */
        @media screen and (max-width: 768px) and (orientation: landscape) {
            .date-display, .day-display {
                position: fixed !important;
                top: 50px !important;
                left: 50% !important;
                transform: translateX(-50%) !important;
                z-index: 999 !important;
                background-color: rgba(0, 0, 0, 0.7) !important;
                padding: 5px 10px !important;
                border-radius: 5px !important;
                color: #71d3ee !important;
                font-size: 0.8em !important;
                text-align: center !important;
                margin: 0 !important;
            }

            .day-display {
                top: 75px !important;
            }

            .text-container {
                margin-top: 100px !important;
                padding-top: 20px !important;
            }

            .vertical-panel {
                height: 60px !important;
                padding: 5px !important;
            }

            .container {
                padding: 5px !important;
                margin-top: 20px !important;
            }

            .analog-clock {
                width: 100px !important;
                height: 100px !important;
            }

            .digital-clock {
                font-size: 1.2em !important;
                padding: 5px 10px !important;
            }

            .countdown-circle {
                width: 100px !important;
                height: 100px !important;
                margin: 10px auto !important;
            }

            .weather-container {
                max-width: 80px !important;
                padding: 4px !important;
            }

            .dates {
                font-size: 0.8em !important;
                padding: 5px !important;
                margin: 5px 0 !important;
            }

            .prayer-times {
                padding: 10px 2px !important;
            }

            .prayer-time {
                margin: 1px !important;
                padding: 3px !important;
            }

            .prayer-name {
                font-size: 0.8em !important;
            }

            .prayer-hour {
                font-size: 0.7em !important;
            }
        }
    </style>
</head>
<body>
    <!-- تم نقل زر تحديث مواقيت الصلاة إلى جانب زر الإعدادات -->
    <div class="zoom-controls">
        <button id="zoom-in-control" title="تكبير">+</button>
        <span id="zoom-level-display">100%</span>
        <button id="zoom-out-control" title="تصغير">−</button>
    </div>
    <div class="date-display" id="dateDisplay"></div>
    <div class="day-display" id="day"></div>

    <div class="vertical-panel">
        <button class="settings-btn">⚙️</button>
        <!-- تم حذف زر تحديث مواقيت الصلاة من الشاشة الرئيسية -->
        <div class="settings-menu" id="settingsMenu">
            <!-- إضافة إعدادات الأذان والإقامة والتعتيم داخل قائمة الإعدادات -->
            <div class="settings-menu-section">
                <h3>إعدادات مواقيت الصلاة</h3>
                <div class="prayer-times-settings">
                    <div class="settings-section">
                        <h4>طريقة حساب مواقيت الصلاة</h4>
                        <div class="setting-group">
                            <label for="calculation-method">اختر طريقة الحساب:</label>
                            <select id="calculation-method">
                                <option value="MWL">رابطة العالم الإسلامي</option>
                                <option value="ISNA">الجمعية الإسلامية لأمريكا الشمالية</option>
                                <option value="Egypt">الهيئة المصرية العامة للمساحة</option>
                                <option value="Makkah">جامعة أم القرى، مكة المكرمة</option>
                                <option value="Karachi">جامعة العلوم الإسلامية، كراتشي</option>
                                <option value="Tehran">معهد الجيوفيزياء، جامعة طهران</option>
                                <option value="Jafari">شيعة اثنا عشرية، معهد ليفا، قم</option>
                                <option value="Gulf">الخليج (15° فجر/isha)</option>
                                <option value="Kuwait">الهيئة العامة الكويتية</option>
                                <option value="Qatar">قطر</option>
                                <option value="Singapore">سنغافورة</option>
                                <option value="France">اتحاد المنظمات الإسلامية في فرنسا</option>
                                <option value="Turkey">الشؤون الدينية التركية</option>
                                <option value="Russia">مجلس المفتين لروسيا</option>
                                <option value="Moonsighting">لجنة رؤية الهلال العالمية</option>
                            </select>
                        </div>

                        <div class="setting-group">
                            <label for="juristic-method">اختر المذهب:</label>
                            <select id="juristic-method">
                                <option value="Shafi">الشافعي (الشافعي، المالكي، الحنبلي)</option>
                                <option value="Hanafi">الحنفي</option>
                            </select>
                        </div>

                        <div class="settings-buttons">
                            <button id="save-city-method" class="save-settings-btn">حفظ إعدادات طريقة الحساب</button>
                            <button id="force-update-prayer-times" class="save-settings-btn" style="background-color: #FF5722; margin-top: 10px;">تحديث مواقيت الصلاة</button>
                        </div>
                    </div>

                    <!-- قسم التعديل اليدوي لمواقيت الصلاة -->
                    <div class="settings-section">
                        <h4>التعديل اليدوي لمواقيت الصلاة</h4>
                        <p class="settings-description">يمكنك تعديل مواقيت الصلاة يدوياً هنا. أدخل الوقت بتنسيق 24 ساعة (مثال: 04:30)</p>

                        <div class="manual-prayer-times">
                            <div class="prayer-time-edit">
                                <label for="manual-fajr">الفجر:</label>
                                <input type="time" id="manual-fajr" class="time-input">
                                <button class="edit-prayer-time-btn" data-prayer="fajr">تعديل</button>
                            </div>

                            <div class="prayer-time-edit">
                                <label for="manual-sunrise">الشروق:</label>
                                <input type="time" id="manual-sunrise" class="time-input">
                                <button class="edit-prayer-time-btn" data-prayer="sunrise">تعديل</button>
                            </div>

                            <div class="prayer-time-edit">
                                <label for="manual-dhuhr">الظهر:</label>
                                <input type="time" id="manual-dhuhr" class="time-input">
                                <button class="edit-prayer-time-btn" data-prayer="dhuhr">تعديل</button>
                            </div>

                            <div class="prayer-time-edit">
                                <label for="manual-asr">العصر:</label>
                                <input type="time" id="manual-asr" class="time-input">
                                <button class="edit-prayer-time-btn" data-prayer="asr">تعديل</button>
                            </div>

                            <div class="prayer-time-edit">
                                <label for="manual-maghrib">المغرب:</label>
                                <input type="time" id="manual-maghrib" class="time-input">
                                <button class="edit-prayer-time-btn" data-prayer="maghrib">تعديل</button>
                            </div>

                            <div class="prayer-time-edit">
                                <label for="manual-isha">العشاء:</label>
                                <input type="time" id="manual-isha" class="time-input">
                                <button class="edit-prayer-time-btn" data-prayer="isha">تعديل</button>
                            </div>
                        </div>

                        <div class="settings-buttons">
                            <button id="save-manual-times" class="save-settings-btn">حفظ جميع المواقيت المعدلة</button>
                            <button id="reset-manual-times" class="save-settings-btn" style="background-color: #FF5722; margin-top: 10px;">إعادة ضبط المواقيت</button>
                        </div>
                    </div>
                </div>

                <h3>إعدادات الإقامة والتعتيم</h3>
                <div class="prayer-settings">
                    <!-- قسم مدة الإقامة -->
                    <div class="iqamah-settings">
                        <label>مدة الإقامة (بالدقائق):</label>
                        <div class="prayer-time-inputs">
                            <div class="prayer-time-input">
                                <label for="fajr-iqama-duration">الفجر:</label>
                                <input type="number" id="fajr-iqama-duration" class="settings-input" min="1" max="40" value="10">
                            </div>
                            <div class="prayer-time-input">
                                <label for="dhuhr-iqama-duration">الظهر:</label>
                                <input type="number" id="dhuhr-iqama-duration" class="settings-input" min="1" max="40" value="10">
                            </div>
                            <div class="prayer-time-input">
                                <label for="asr-iqama-duration">العصر:</label>
                                <input type="number" id="asr-iqama-duration" class="settings-input" min="1" max="40" value="10">
                            </div>
                            <div class="prayer-time-input">
                                <label for="maghrib-iqama-duration">المغرب:</label>
                                <input type="number" id="maghrib-iqama-duration" class="settings-input" min="1" max="40" value="10">
                            </div>
                            <div class="prayer-time-input">
                                <label for="isha-iqama-duration">العشاء:</label>
                                <input type="number" id="isha-iqama-duration" class="settings-input" min="1" max="40" value="10">
                            </div>
                        </div>
                        <button id="save-iqamah-times" class="settings-button">حفظ مدة الإقامة</button>
                        <button id="test-iqamah-times" class="settings-button" style="margin-top: 10px;">اختبار مدة الإقامة</button>
                        <button id="test-iqamah-countdown" class="settings-button" style="margin-top: 10px; background-color: #4CAF50;">اختبار العد التنازلي للإقامة</button>
                    </div>

                    <!-- قسم مدة التعتيم -->
                    <div class="darkness-settings">
                        <label>مدة التعتيم بعد الإقامة (بالدقائق):</label>
                        <div class="prayer-time-inputs">
                            <div class="prayer-time-input">
                                <label for="fajr-darkness">الفجر:</label>
                                <input type="number" id="fajr-darkness" class="settings-input" min="0" max="60" value="0">
                            </div>
                            <div class="prayer-time-input">
                                <label for="dhuhr-darkness">الظهر:</label>
                                <input type="number" id="dhuhr-darkness" class="settings-input" min="0" max="60" value="0">
                            </div>
                            <div class="prayer-time-input">
                                <label for="asr-darkness">العصر:</label>
                                <input type="number" id="asr-darkness" class="settings-input" min="0" max="60" value="0">
                            </div>
                            <div class="prayer-time-input">
                                <label for="maghrib-darkness">المغرب:</label>
                                <input type="number" id="maghrib-darkness" class="settings-input" min="0" max="60" value="0">
                            </div>
                            <div class="prayer-time-input">
                                <label for="isha-darkness">العشاء:</label>
                                <input type="number" id="isha-darkness" class="settings-input" min="0" max="60" value="0">
                            </div>
                        </div>
                        <button id="save-darkness-times" class="settings-button">حفظ مدة التعتيم</button>
                    </div>
                </div>

                <h3>إعدادات الأذان</h3>
                <div class="adhan-settings">
                    <label>
                        <input type="checkbox" id="enable-adhan" checked>
                        تفعيل الأذان
                    </label>
                    <div class="volume-control">
                        <label for="adhan-volume">مستوى الصوت:</label>
                        <input type="range" id="adhan-volume" min="0" max="1" step="0.1" value="1">
                    </div>
                    <button id="save-adhan-settings" class="save-settings-btn">حفظ إعدادات الأذان</button>
                </div>
            </div>
            <div class="location-settings">
                <label for="country-select">اختر الدولة:</label>
                <select id="country-select" class="settings-select">
                    <option value="">اختر الدولة</option>
                    <option value="الأردن">الأردن</option>
                    <option value="السعودية">السعودية</option>
                    <option value="مصر">مصر</option>
                    <option value="الإمارات">الإمارات</option>
                    <option value="قطر">قطر</option>
                    <option value="الكويت">الكويت</option>
                    <option value="عُمان">عُمان</option>
                    <option value="البحرين">البحرين</option>
                    <option value="لبنان">لبنان</option>
                    <option value="فلسطين">فلسطين</option>
                    <option value="العراق">العراق</option>
                    <option value="سوريا">سوريا</option>
                    <option value="تركيا">تركيا</option>
                </select>

                <label for="city-select">اختر المدينة:</label>
                <select id="city-select" class="settings-select">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </select>
            </div>

            <!-- قسم التعديل اليدوي لمواقيت الصلاة -->
            <div class="settings-section">
                <h4>التعديل اليدوي لمواقيت الصلاة</h4>
                <p class="settings-description">يمكنك تعديل مواقيت الصلاة يدوياً هنا. سيتم حفظ التعديلات واستخدامها بدلاً من المواقيت المحسوبة.</p>

                <div class="manual-prayer-times">
                    <div class="prayer-time-edit">
                        <label for="manual-fajr">الفجر:</label>
                        <input type="text" id="manual-fajr" class="time-input" placeholder="00:00">
                        <button class="edit-prayer-time-btn" data-prayer="fajr">تعديل</button>
                    </div>

                    <div class="prayer-time-edit">
                        <label for="manual-sunrise">الشروق:</label>
                        <input type="text" id="manual-sunrise" class="time-input" placeholder="00:00">
                        <button class="edit-prayer-time-btn" data-prayer="sunrise">تعديل</button>
                    </div>

                    <div class="prayer-time-edit">
                        <label for="manual-dhuhr">الظهر:</label>
                        <input type="text" id="manual-dhuhr" class="time-input" placeholder="00:00">
                        <button class="edit-prayer-time-btn" data-prayer="dhuhr">تعديل</button>
                    </div>

                    <div class="prayer-time-edit">
                        <label for="manual-asr">العصر:</label>
                        <input type="text" id="manual-asr" class="time-input" placeholder="00:00">
                        <button class="edit-prayer-time-btn" data-prayer="asr">تعديل</button>
                    </div>

                    <div class="prayer-time-edit">
                        <label for="manual-maghrib">المغرب:</label>
                        <input type="text" id="manual-maghrib" class="time-input" placeholder="00:00">
                        <button class="edit-prayer-time-btn" data-prayer="maghrib">تعديل</button>
                    </div>

                    <div class="prayer-time-edit">
                        <label for="manual-isha">العشاء:</label>
                        <input type="text" id="manual-isha" class="time-input" placeholder="00:00">
                        <button class="edit-prayer-time-btn" data-prayer="isha">تعديل</button>
                    </div>

                    <div class="prayer-times-buttons">
                        <button id="save-manual-times" class="settings-button">حفظ جميع المواقيت المعدلة</button>
                        <button id="reset-manual-times" class="settings-button">إعادة ضبط المواقيت</button>
                    </div>
                </div>
            </div>

            <label>اختيار الخلفية:</label>
            <select id="backgroundSelect">
                <option value="backgrounds/background1.jpg">خلفية 1</option>
                <option value="backgrounds/background2.jpg">خلفية 2</option>
                <option value="backgrounds/background3.jpg">خلفية 3</option>
                <option value="backgrounds/background4.jpg">خلفية 4</option>
                <option value="backgrounds/background5.jpg">خلفية 5</option>
                <option value="backgrounds/background6.jpg">خلفية 6</option>
                <option value="backgrounds/16.webp">خلفية 16</option>
                <option value="backgrounds/17.webp">خلفية 17</option>
                <option value="backgrounds/20.webp">خلفية 20</option>
                <option value="backgrounds/21.webp">خلفية 21</option>
                <option value="backgrounds/22.webp">خلفية 22</option>
                <option value="backgrounds/100.webp">خلفية 100</option>
                <option value="backgrounds/101.webp">خلفية 101</option>
                <option value="backgrounds/102.webp">خلفية 102</option>
                    <option value="backgrounds/103.webp">خلفية 103</option>
                    <option value="backgrounds/104.webp">خلفية 104</option>
                    <option value="backgrounds/105.webp">خلفية 105</option>
                    <option value="backgrounds/106.webp">خلفية 106</option>
                    <option value="backgrounds/107.webp">خلفية 107</option>
                    <option value="backgrounds/108.webp">خلفية 108</option>
                    <option value="backgrounds/109.webp">خلفية 109</option>
                    <option value="backgrounds/110.webp">خلفية 110</option>
                    <option value="backgrounds/111.webp">خلفية 111</option>
                    <option value="backgrounds/112.webp">خلفية 112</option>
                    <option value="backgrounds/113.webp">خلفية 113</option>
                    <option value="backgrounds/114.webp">خلفية 114</option>
                    <option value="backgrounds/115.webp">خلفية 115</option>
                    <option value="backgrounds/HR-0.jpg">خلفية HR-0</option>
                    <option value="backgrounds/HR-1.jpg">خلفية HR-1</option>
                    <option value="backgrounds/HR-2.jpg">خلفية HR-2</option>
                    <option value="backgrounds/HR-12.jpg">خلفية HR-12</option>
                    <option value="backgrounds/HR-23.jpg">خلفية HR-23</option>
                    <option value="backgrounds/HR-26.jpg">خلفية HR-26</option>
                    <option value="backgrounds/HR-14.jpg">خلفية HR-14</option>
                    <option value="backgrounds/HR-22.jpg">خلفية HR-22</option>
            </select>

            <label>صوت الأذان:</label>
            <div class="mosque-settings">
                <label for="mosque-name-input">اسم المسجد:</label>
                <input type="text" id="mosque-name-input" class="settings-input" placeholder="ادخل اسم المسجد">
                <div class="font-size-controls">
                    <button id="decrease-mosque-text" class="font-size-btn">-</button>
                    <span id="mosque-font-size">24</span>
                    <button id="increase-mosque-text" class="font-size-btn">+</button>
                </div>
                <div class="color-control">
                    <label for="mosque-text-color">لون الخط:</label>
                    <input type="color" id="mosque-text-color" value="#D4AF37">
                </div>
            </div>

            <!-- قسم التحكم في ألوان المستطيلات -->
            <div class="color-settings">
                <h4>إعدادات الألوان</h4>

                <!-- ألوان الشريط الجانبي -->
                <div class="color-section">
                    <label>ألوان الشريط الجانبي:</label>
                    <div class="color-control">
                        <label for="sidebar-bg-color">لون خلفية الشريط الجانبي:</label>
                        <input type="color" id="sidebar-bg-color" value="#4a3b3b">
                    </div>
                    <div class="color-control">
                        <label for="sidebar-text-color">لون نص الشريط الجانبي:</label>
                        <input type="color" id="sidebar-text-color" value="#71d3ee">
                    </div>
                </div>

                <!-- ألوان شريط مواقيت الصلاة -->
                <div class="color-section">
                    <label>ألوان شريط مواقيت الصلاة:</label>
                    <div class="color-control">
                        <label for="prayer-bar-bg-color">لون خلفية شريط المواقيت:</label>
                        <input type="color" id="prayer-bar-bg-color" value="#4a3b3b">
                    </div>
                    <div class="color-control">
                        <label for="prayer-bar-text-color">لون نص شريط المواقيت:</label>
                        <input type="color" id="prayer-bar-text-color" value="#71d3ee">
                    </div>
                    <div class="color-control">
                        <label for="prayer-time-color">لون أوقات الصلاة:</label>
                        <input type="color" id="prayer-time-color" value="#40E0D0">
                    </div>
                </div>

                <!-- ألوان الساعة الرقمية -->
                <div class="color-section">
                    <label>ألوان الساعة الرقمية:</label>
                    <div class="color-control">
                        <label for="digital-clock-bg-color">لون خلفية الساعة الرقمية:</label>
                        <input type="color" id="digital-clock-bg-color" value="#000000">
                    </div>
                    <div class="color-control">
                        <label for="digital-clock-text-color">لون نص الساعة الرقمية:</label>
                        <input type="color" id="digital-clock-text-color" value="#71d3ee">
                    </div>
                    <div class="color-control">
                        <label for="digital-clock-border-color">لون حدود الساعة الرقمية:</label>
                        <input type="color" id="digital-clock-border-color" value="#71d3ee">
                    </div>
                </div>

                <!-- ألوان العد التنازلي -->
                <div class="color-section">
                    <label>ألوان العد التنازلي:</label>
                    <div class="color-control">
                        <label for="countdown-bg-color">لون خلفية العد التنازلي:</label>
                        <input type="color" id="countdown-bg-color" value="#ffffff">
                    </div>
                    <div class="color-control">
                        <label for="countdown-border-color">لون حدود العد التنازلي:</label>
                        <input type="color" id="countdown-border-color" value="#71d3ee">
                    </div>
                </div>

                <!-- أزرار التحكم في الألوان -->
                <div class="color-buttons">
                    <button id="save-colors" class="settings-button">حفظ الألوان</button>
                    <button id="reset-colors" class="settings-button" style="background-color: #FF5722; margin-top: 10px;">إعادة ضبط الألوان</button>
                </div>
            </div>

            <select id="adhan-sound">
                <option value="audio/audio_dhar.mp3">أذان باقي الصلوات</option>
                <option value="audio/audio_fajr.mp3">أذان الفجر</option>
            </select>
            <button id="play-sound">تشغيل الصوت</button>
            <button id="stop-sound">إيقاف الصوت</button>

            <!-- زر اختبار الأذان والإقامة -->
            <button id="test-adhan-iqama" style="background-color: #ff5722; color: white; padding: 10px; margin-top: 10px; border: none; border-radius: 5px; cursor: pointer;">اختبار الأذان والإقامة</button>

            <!-- تم دمج إعدادات الأذان في قسم واحد أعلاه -->

            <!-- تم نقل عنصر الصوت إلى أسفل الصفحة -->

            <script>
                // الحصول على عناصر HTML
                const adhanSoundSelect = document.getElementById('adhan-sound');
                const playSoundButton = document.getElementById('play-sound');
                const stopSoundButton = document.getElementById('stop-sound');
                const adhanAudio = document.getElementById('adhan-audio');
                // استخدام معرف فريد لتجنب التكرار
                const enableAdhanCheckbox = document.getElementById('enable-adhan');

                // وظيفة لتشغيل الصوت
                function playSound() {
                    const selectedSound = adhanSoundSelect.value;
                    adhanAudio.src = selectedSound;
                    adhanAudio.play();
                }

                // وظيفة لإيقاف الصوت
                function stopSound() {
                    adhanAudio.pause();
                    adhanAudio.currentTime = 0;
                }

                // إضافة مستمعي الأحداث للأزرار
                playSoundButton.addEventListener('click', playSound);
                stopSoundButton.addEventListener('click', stopSound);

                // إضافة مستمع لزر اختبار الأذان والإقامة
                const testAdhanIqamaButton = document.getElementById('test-adhan-iqama');
                if (testAdhanIqamaButton) {
                    testAdhanIqamaButton.addEventListener('click', function() {
                        // تشغيل الأذان - استخدام المتغير العالمي بدلاً من إعادة تعريفه
                        if (adhanAudio) {
                            adhanAudio.src = adhanSoundSelect.value;
                            adhanAudio.currentTime = 0;
                            adhanAudio.volume = parseFloat(document.getElementById('adhan-volume')?.value || 1);
                            adhanAudio.play();
                        }

                        // بدء العد التنازلي للإقامة
                        startIqamahCountdown('asr', 'العصر');
                    });
                }
            </script>
            <label>تغيير حجم النص:</label>
            <button id="increase-text-size">تكبير النص</button>
            <button id="decrease-text-size">تصغير النص</button>

            <label>تغيير لون النص:</label>
            <select id="text-color-select">
                <option value="#FFFFFF">أبيض</option>
                <option value="#D4AF37">ذهبي</option>
                <option value="#FFD700">أصفر</option>
                <option value="#00FF00">أخضر</option>
                <option value="#FF0000">أحمر</option>
                <option value="#0000FF">أزرق</option>
                <option value="#FF1493">وردي</option>
                <option value="#9400D3">بنفسجي</option>
                <option value="#FFA500">برتقالي</option>
                <option value="#40E0D0">تركواز</option>
                <option value="#FF69B4">زهري</option>
                <option value="#98FB98">أخضر فاتح</option>
                <option value="#87CEEB">أزرق سماوي</option>
                <option value="#DDA0DD">بنفسجي فاتح</option>
            </select>
            <label>نظام الوقت:</label>
            <select id="time-format-select">
                <option value="12">12 ساعة</option>
                <option value="24">24 ساعة</option>
            </select>

            <label>التوقيت:</label>
            <select id="seasonal-time" class="settings-select">
                <option value="winter">التوقيت الشتوي</option>
                <option value="summer">التوقيت الصيفي</option>
            </select>

            <label>المنطقة الزمنية:</label>
            <select id="timezone-select" class="settings-select">
                <option value="Asia/Amman">توقيت عمان</option>
                <option value="Asia/Riyadh">توقيت الرياض</option>
                <option value="Asia/Dubai">توقيت دبي</option>
                <option value="Asia/Makkah">توقيت مكة المكرمة</option>
            </select>

            <!-- إضافة قسم حجم الشاشة -->
            <label>حجم الشاشة:</label>
            <div class="screen-size-buttons">
                <button class="screen-size-button" data-size="mobile">جوال</button>
                <button class="screen-size-button" data-size="tablet">تابلت</button>
                <button class="screen-size-button" data-size="laptop">لابتوب</button>
                <button class="screen-size-button" data-size="tv">تلفاز</button>
            </div>

            <!-- إضافة قسم اتجاه العرض -->
            <label>اتجاه العرض:</label>
            <div class="orientation-buttons">
                <button class="orientation-button" data-orientation="portrait">عامودي</button>
                <button class="orientation-button" data-orientation="landscape">أفقي</button>
            </div>

            <!-- إضافة قسم التحكم في حجم الشاشة -->
            <label>حجم الشاشة:</label>
            <div class="screen-size-controls">
                <button id="decrease-screen-size-control" class="screen-size-btn">-</button>
                <span id="screen-size-value-display">100%</span>
                <button id="increase-screen-size-control" class="screen-size-btn">+</button>
            </div>
            <div class="preset-sizes">
                <button class="preset-size-btn" data-size="50">50%</button>
                <button class="preset-size-btn" data-size="75">75%</button>
                <button class="preset-size-btn" data-size="100">100%</button>
                <button class="preset-size-btn" data-size="125">125%</button>
                <button class="preset-size-btn" data-size="150">150%</button>
            </div>

            <!-- تم نقل أقسام مدة الإقامة ومدة التعتيم وتعديل المواقيت إلى قسم الإعدادات -->
        </div>

    <div class="main-content">
        <div class="container">
            <div class="weather-container">
                <div class="weather-display">
                    <img src="https://openweathermap.org/img/wn/<EMAIL>" alt="حالة الطقس" width="50" height="50">
                    <div class="temperature">--°C</div>
                    <div class="description">جاري التحميل...</div>
                </div>
            </div>

            <div class="dates">
                <div class="gregorian-date"></div>
                <div class="hijri-date"></div>
            </div>

            <div class="analog-clock">
                <div class="hand hour-hand"></div>
                <div class="hand minute-hand"></div>
                <div class="hand second-hand"></div>
                <div class="center-dot"></div>
            </div>
            <div class="digital-clock"></div>

            <div class="countdown-circle">
                <div class="countdown-wrapper">
                    <div class="countdown-time">00:00</div>
                    <div class="next-prayer-text">ننتظر صلاة العصر</div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-container">
        <div id="text-overlay" class="text-overlay"></div>
    </div>

    <div class="prayer-times">
        <div class="prayer-time">
            <div class="prayer-name">الفجر</div>
            <div class="prayer-hour" id="fajr-time"></div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">الشروق</div>
            <div class="prayer-hour" id="sunrise-time"></div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">الظهر</div>
            <div class="prayer-hour" id="dhuhr-time"></div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">العصر</div>
            <div class="prayer-hour" id="asr-time"></div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">المغرب</div>
            <div class="prayer-hour" id="maghrib-time"></div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">العشاء</div>
            <div class="prayer-hour" id="isha-time"></div>
        </div>
    </div>

    <!-- إضافة عنصر الصوت للأذان -->
    <audio id="adhan-audio" src="audio/short_azan.mp3"></audio>
    <audio id="iqama-audio" src="audio/short_iqama.mp3"></audio>

    <!-- إضافة مكتبة حساب مواقيت الصلاة -->
    <script src="js/PrayTimes.js"></script>

    <!-- إضافة ملف العد التنازلي للإقامة -->
    <script src="iqama-countdown.js"></script>

    <!-- تم نقل إعدادات الأذان والإقامة والتعتيم إلى داخل قائمة الإعدادات -->

    <!-- إضافة عنصر عرض العد التنازلي للإقامة -->
    <div class="iqamah-countdown" style="display: none;">
        <div class="countdown-circle">
            <div class="countdown-wrapper">
                <div id="iqamah-label">الإقامة</div>
                <div id="iqamah-time">00:00</div>
                <div id="iqamah-prayer-name"></div>
            </div>
        </div>
    </div>

    <!-- إضافة عنصر الإشعارات -->
    <div id="notification" class="notification" style="display: none;"></div>


    <script>
        // المتغيرات العالمية
        let currentDate = new Date().toISOString().split('T')[0]; // تخزين التاريخ الحالي
        let checkDateInterval; // متغير لتخزين مؤقت فحص تغير التاريخ
        let manualPrayerTimesEnabled = localStorage.getItem('manualPrayerTimesEnabled') === 'true'; // متغير للتحكم في استخدام المواقيت المعدلة يدوياً
        let manualPrayerTimes = JSON.parse(localStorage.getItem('manualPrayerTimes')) || {}; // متغير لتخزين المواقيت المعدلة يدوياً
        let autoUpdateInterval; // متغير لتخزين مؤقت التحديث التلقائي للمواقيت

        // تعريف متغير لإعدادات التعتيم
        let darknessTimes = JSON.parse(localStorage.getItem('darknessTimes')) || {
            fajr: 10,
            dhuhr: 10,
            asr: 10,
            maghrib: 10,
            isha: 10
        };

        console.log('تم تهيئة المتغيرات العالمية:');
        console.log('- المواقيت المعدلة يدوياً:', manualPrayerTimes);
        console.log('- حالة المواقيت المعدلة يدوياً:', manualPrayerTimesEnabled ? 'مفعلة' : 'غير مفعلة');
        console.log('- إعدادات التعتيم:', darknessTimes);

        // مدة الإقامة بالدقائق لكل صلاة
        let iqamahTimes = JSON.parse(localStorage.getItem('iqama_durations')) || {
            fajr: 15,
            dhuhr: 10,
            asr: 10,
            maghrib: 7,
            isha: 10
        };

        // مواقيت الصلاة الثابتة للمدن المختلفة (تستخدم كاحتياطي)
        window.AMMAN_PRAYER_TIMES = {
            'Asia/Amman': {
                fajr: '03:55',
                sunrise: '05:25',
                dhuhr: '12:35',
                asr: '16:15',
                maghrib: '19:45',
                isha: '21:15'
            },
            'Asia/Riyadh': {
                fajr: '03:45',
                sunrise: '05:15',
                dhuhr: '12:00',
                asr: '15:30',
                maghrib: '18:45',
                isha: '20:15'
            },
            'Asia/Dubai': {
                fajr: '04:05',
                sunrise: '05:35',
                dhuhr: '12:20',
                asr: '15:45',
                maghrib: '19:05',
                isha: '20:35'
            },
            'Africa/Cairo': {
                fajr: '03:35',
                sunrise: '05:05',
                dhuhr: '12:10',
                asr: '15:50',
                maghrib: '19:15',
                isha: '20:45'
            },
            'Asia/Jerusalem': {
                fajr: '03:50',
                sunrise: '05:20',
                dhuhr: '12:30',
                asr: '16:10',
                maghrib: '19:40',
                isha: '21:10'
            },
            'Asia/Makkah': {
                fajr: '04:15',
                sunrise: '05:45',
                dhuhr: '12:25',
                asr: '15:55',
                maghrib: '19:05',
                isha: '20:35'
            },
            'Asia/Madinah': {
                fajr: '04:10',
                sunrise: '05:40',
                dhuhr: '12:20',
                asr: '15:50',
                maghrib: '19:00',
                isha: '20:30'
            },
            'Asia/Istanbul': {
                fajr: '03:30',
                sunrise: '05:00',
                dhuhr: '12:45',
                asr: '16:30',
                maghrib: '20:30',
                isha: '22:00'
            },
            'Asia/Baghdad': {
                fajr: '03:40',
                sunrise: '05:10',
                dhuhr: '12:15',
                asr: '15:55',
                maghrib: '19:20',
                isha: '20:50'
            },
            'Asia/Kuwait': {
                fajr: '03:35',
                sunrise: '05:05',
                dhuhr: '11:50',
                asr: '15:25',
                maghrib: '18:35',
                isha: '20:05'
            },
            'Asia/Doha': {
                fajr: '03:30',
                sunrise: '05:00',
                dhuhr: '11:45',
                asr: '15:20',
                maghrib: '18:30',
                isha: '20:00'
            },
            'Asia/Muscat': {
                fajr: '04:00',
                sunrise: '05:30',
                dhuhr: '12:15',
                asr: '15:45',
                maghrib: '19:00',
                isha: '20:30'
            },
            'Asia/Beirut': {
                fajr: '03:45',
                sunrise: '05:15',
                dhuhr: '12:25',
                asr: '16:05',
                maghrib: '19:35',
                isha: '21:05'
            },
            'Africa/Tunis': {
                fajr: '03:40',
                sunrise: '05:10',
                dhuhr: '12:40',
                asr: '16:25',
                maghrib: '20:10',
                isha: '21:40'
            },
            'Africa/Algiers': {
                fajr: '03:45',
                sunrise: '05:15',
                dhuhr: '12:45',
                asr: '16:30',
                maghrib: '20:15',
                isha: '21:45'
            },
            'Africa/Casablanca': {
                fajr: '04:00',
                sunrise: '05:30',
                dhuhr: '13:00',
                asr: '16:45',
                maghrib: '20:30',
                isha: '22:00'
            },
            'Asia/Tehran': {
                fajr: '04:10',
                sunrise: '05:40',
                dhuhr: '13:10',
                asr: '16:55',
                maghrib: '20:40',
                isha: '22:10'
            },
            'Asia/Karachi': {
                fajr: '04:20',
                sunrise: '05:50',
                dhuhr: '12:30',
                asr: '16:05',
                maghrib: '19:10',
                isha: '20:40'
            },
            'Asia/Kuala_Lumpur': {
                fajr: '05:40',
                sunrise: '07:10',
                dhuhr: '13:15',
                asr: '16:40',
                maghrib: '19:20',
                isha: '20:50'
            },
            'Asia/Jakarta': {
                fajr: '04:35',
                sunrise: '06:05',
                dhuhr: '12:10',
                asr: '15:35',
                maghrib: '18:15',
                isha: '19:45'
            }
        };

        // عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('تم تحميل الصفحة');

            // 1. إنشاء المستطيل الأفقي لمواقيت الصلاة أولاً
            createPrayerTimesBar();
            console.log('تم إنشاء المستطيل الأفقي لمواقيت الصلاة');

            // تحديث حالة الطقس
            updateWeather();
            // تحديث حالة الطقس كل ساعة
            setInterval(updateWeather, 3600000);

            // دالة لملء قائمة المدن
            function fillCityOptions() {
                try {
                    const citySelect = document.getElementById('selected-city');
                    if (!citySelect) {
                        console.warn('عنصر قائمة المدن غير موجود');
                        return;
                    }

                    // مسح القائمة الحالية
                    citySelect.innerHTML = '';

                    // إضافة المدن المتاحة
                    const cities = [
                        { value: 'Asia/Amman', text: 'عمان' },
                        { value: 'Asia/Riyadh', text: 'الرياض' },
                        { value: 'Asia/Dubai', text: 'دبي' },
                        { value: 'Asia/Makkah', text: 'مكة المكرمة' },
                        { value: 'Asia/Madinah', text: 'المدينة المنورة' },
                        { value: 'Asia/Jerusalem', text: 'القدس' },
                        { value: 'Asia/Baghdad', text: 'بغداد' },
                        { value: 'Africa/Cairo', text: 'القاهرة' },
                        { value: 'Africa/Tunis', text: 'تونس' },
                        { value: 'Africa/Algiers', text: 'الجزائر' },
                        { value: 'Africa/Casablanca', text: 'الدار البيضاء' },
                        { value: 'Asia/Istanbul', text: 'إسطنبول' },
                        { value: 'Asia/Tehran', text: 'طهران' },
                        { value: 'Asia/Karachi', text: 'كراتشي' },
                        { value: 'Asia/Kuala_Lumpur', text: 'كوالالمبور' },
                        { value: 'Asia/Jakarta', text: 'جاكرتا' }
                    ];

                    // إضافة المدن إلى القائمة
                    cities.forEach(city => {
                        const option = document.createElement('option');
                        option.value = city.value;
                        option.textContent = city.text;
                        citySelect.appendChild(option);
                    });

                    // تعيين المدينة المحفوظة
                    const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                    citySelect.value = savedCity;

                    console.log('تم ملء قائمة المدن بنجاح');
                } catch (error) {
                    console.error('خطأ في ملء قائمة المدن:', error);
                }
            }

            // ملء قائمة المدن
            fillCityOptions();

            // تحديث التاريخ والوقت
            updateDate();
            setInterval(updateDate, 60000);

            // 2. جلب أوقات الصلاة
            try {
                await getPrayerTimes();
                console.log('تم جلب أوقات الصلاة بنجاح');

                // 3. بعد جلب أوقات الصلاة، قم بتحديث قائمة الصلوات القادمة
                displayRemainingPrayerTimes();

                // 4. بدء العد التنازلي للصلاة القادمة
                startCountdownTimer();

                // تحديث نص الصلاة القادمة مباشرة
                updateNextPrayerText();

                // تحديث مواقيت الصلاة في المستطيل الأفقي مباشرة
                updatePrayerTimes();

                // تعيين نص العد التنازلي كقيمة أولية
                const countdownTime = document.querySelector('.countdown-time');
                if (countdownTime) {
                    countdownTime.innerHTML = `<span style="font-weight: 900; color: #ff0000; text-shadow: 0 0 3px rgba(255, 0, 0, 0.5);">00:00</span>`;
                }

                // 5. تحديث أوقات الصلاة كل ساعة
                setInterval(getPrayerTimes, 3600000);

                // 6. تحديث المستطيل الأفقي كل دقيقة
                setInterval(updatePrayerTimes, 60000);
            } catch (error) {
                console.error('خطأ في جلب أوقات الصلاة:', error);

                // في حالة الخطأ، استخدم المواقيت الثابتة
                console.log('استخدام المواقيت الثابتة كاحتياطي...');
                const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                const defaultTimes = window.AMMAN_PRAYER_TIMES[currentCity] || window.AMMAN_PRAYER_TIMES['Asia/Amman'];

                // تحديث المواقيت العالمية
                window.prayerTimes = {
                    [currentCity]: defaultTimes
                };

                // تحديث العرض
                updatePrayerTimesDisplay(defaultTimes, '24');
                displayRemainingPrayerTimes();
                startCountdownTimer();
                updateNextPrayerText();

                // تحديث المستطيل الأفقي كل دقيقة
                setInterval(updatePrayerTimes, 60000);
            }

            // بدء تحديث الساعة
            startClockUpdate();

            // إعداد مستمعي الأحداث للمدينة وطريقة الحساب
            setupCityAndMethodListeners();

            // إعداد مستمعي الأحداث لإعدادات الأذان
            setupAdhanListeners();

            // تحميل إعدادات الأذان المحفوظة
            loadAdhanSettings();

            // إعداد مستمع لزر تحديث مواقيت الصلاة
            setupUpdatePrayerTimesButton();
        });

        // دالة لإعداد مستمعي الأحداث للمدينة وطريقة الحساب
        function setupCityAndMethodListeners() {
            // إعداد مستمع لتغيير المدينة
            const citySelect = document.getElementById('selected-city');
            if (citySelect) {
                // تعيين القيمة المحفوظة
                const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                citySelect.value = savedCity;

                // إضافة مستمع للتغيير
                citySelect.addEventListener('change', async (e) => {
                    const selectedCity = e.target.value;
                    localStorage.setItem('selectedCity', selectedCity);

                    try {
                        console.log('تم تغيير المدينة إلى:', selectedCity);

                        // حذف المواقيت المحسوبة للمدينة الحالية لإجبار النظام على إعادة حسابها
                        if (window.prayerTimes && window.prayerTimes[selectedCity]) {
                            delete window.prayerTimes[selectedCity];
                        }

                        // التحقق من وجود مواقيت معدلة يدوياً للمدينة الجديدة
                        if (manualPrayerTimesEnabled && manualPrayerTimes && manualPrayerTimes[selectedCity]) {
                            console.log('تم العثور على مواقيت معدلة يدوياً للمدينة الجديدة:', selectedCity);

                            // تطبيق المواقيت المعدلة يدوياً
                            applyManualPrayerTimes();

                            // تحديث حقول التعديل اليدوي
                            updateManualPrayerTimeInputs();

                            // تحديث العرض
                            updatePrayerTimes();
                            displayRemainingPrayerTimes();
                            updateCountdown();
                            updateNextPrayerText();

                            // عرض إشعار
                            showNotification(`تم تغيير المدينة إلى ${citySelect.options[citySelect.selectedIndex].text} وتطبيق المواقيت المعدلة يدوياً`, 'success');
                        } else {
                            // جلب أوقات الصلاة للمدينة الجديدة
                            await getPrayerTimes();
                            console.log('تم تحديث أوقات الصلاة للمدينة الجديدة:', selectedCity);

                            // تحديث العرض
                            updatePrayerTimes();
                            displayRemainingPrayerTimes();
                            updateCountdown();
                            updateNextPrayerText();

                            // تحديث حقول التعديل اليدوي
                            updateManualPrayerTimeInputs();

                            // عرض إشعار
                            showNotification(`تم تغيير المدينة إلى ${citySelect.options[citySelect.selectedIndex].text}`, 'success');
                        }
                    } catch (error) {
                        console.error('خطأ في تحديث أوقات الصلاة عند تغيير المدينة:', error);
                        showNotification('حدث خطأ أثناء تحديث أوقات الصلاة', 'error');
                    }
                });
            }

            // إعداد مستمع لتغيير المذهب
            const juristicMethodSelect = document.getElementById('juristic-method');
            if (juristicMethodSelect) {
                // تعيين القيمة المحفوظة
                const savedJuristicMethod = localStorage.getItem('juristicMethod') || 'Shafi';
                juristicMethodSelect.value = savedJuristicMethod;

                // إضافة مستمع للتغيير
                juristicMethodSelect.addEventListener('change', async (e) => {
                    const selectedJuristicMethod = e.target.value;
                    localStorage.setItem('juristicMethod', selectedJuristicMethod);

                    try {
                        console.log('تم تغيير المذهب إلى:', selectedJuristicMethod);

                        // حذف المواقيت المحفوظة للمدينة الحالية لإجبار النظام على إعادة حسابها
                        const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                        if (window.prayerTimes && window.prayerTimes[currentCity]) {
                            delete window.prayerTimes[currentCity];
                        }

                        // تعيين المذهب في مكتبة PrayTimes.js
                        if (window.prayTimes) {
                            const juristicMethodValue = selectedJuristicMethod === 'Hanafi' ? 1 : 0;
                            window.prayTimes.adjust({asr: juristicMethodValue});
                        }

                        // جلب أوقات الصلاة بالمذهب الجديد
                        await getPrayerTimes();
                        console.log('تم تحديث أوقات الصلاة بالمذهب الجديد:', selectedJuristicMethod);

                        // تحديث العرض
                        updatePrayerTimes(); // تحديث المستطيل الأفقي
                        displayRemainingPrayerTimes();
                        updateCountdown();
                        updateNextPrayerText();

                        // عرض إشعار
                        showNotification(`تم تغيير المذهب إلى ${juristicMethodSelect.options[juristicMethodSelect.selectedIndex].text}`, 'success');
                    } catch (error) {
                        console.error('خطأ في تحديث أوقات الصلاة عند تغيير المذهب:', error);
                        showNotification('حدث خطأ أثناء تحديث أوقات الصلاة', 'error');
                    }
                });
            }

            // إعداد مستمع لتغيير طريقة الحساب
            const methodSelect = document.getElementById('calculation-method');
            if (methodSelect) {
                // تعيين القيمة المحفوظة
                const savedMethod = localStorage.getItem('calculationMethod') || 'MWL';
                methodSelect.value = savedMethod;

                // إضافة مستمع للتغيير
                methodSelect.addEventListener('change', async (e) => {
                    const selectedMethod = e.target.value;
                    localStorage.setItem('calculationMethod', selectedMethod);

                    try {
                        console.log('تم تغيير طريقة الحساب إلى:', selectedMethod);

                        // عرض إشعار
                        showNotification('جاري تحديث مواقيت الصلاة...', 'info');

                        // تأخير صغير لتحسين تجربة المستخدم
                        setTimeout(async function() {
                            try {
                                // تحديث إجباري لمواقيت الصلاة
                                forceUpdatePrayerTimes();

                                // عرض إشعار
                                showNotification(`تم تغيير طريقة الحساب إلى ${methodSelect.options[methodSelect.selectedIndex].text}`, 'success');
                            } catch (error) {
                                console.error('خطأ في تحديث أوقات الصلاة عند تغيير طريقة الحساب:', error);
                                showNotification('حدث خطأ أثناء تحديث أوقات الصلاة', 'error');
                            }
                        }, 500);
                    } catch (error) {
                        console.error('خطأ في تغيير طريقة الحساب:', error);
                        showNotification('حدث خطأ أثناء تغيير طريقة الحساب', 'error');
                    }
                });
            }

            // إعداد مستمع لزر حفظ الإعدادات
            const saveCityMethodBtn = document.getElementById('save-city-method');
            if (saveCityMethodBtn) {
                saveCityMethodBtn.addEventListener('click', async () => {
                    try {
                        // الحصول على القيم المحددة
                        const selectedCity = citySelect?.value || 'Asia/Amman';
                        const selectedMethod = methodSelect?.value || 'MWL';
                        const selectedJuristicMethod = juristicMethodSelect?.value || 'Shafi';

                        // حفظ القيم في التخزين المحلي
                        localStorage.setItem('selectedCity', selectedCity);
                        localStorage.setItem('calculationMethod', selectedMethod);
                        localStorage.setItem('juristicMethod', selectedJuristicMethod);

                        // تعيين طريقة الحساب والمذهب في مكتبة PrayTimes.js
                        if (window.prayTimes) {
                            // تعيين طريقة الحساب
                            window.prayTimes.setMethod(selectedMethod);

                            // تعيين المذهب (للعصر)
                            const juristicMethodValue = selectedJuristicMethod === 'Hanafi' ? 1 : 0;
                            window.prayTimes.adjust({asr: juristicMethodValue});
                        }

                        // جلب أوقات الصلاة بالإعدادات الجديدة
                        await getPrayerTimes();

                        // تحديث العرض
                        displayRemainingPrayerTimes();
                        updateCountdown();
                        updateNextPrayerText();

                        // عرض إشعار
                        showNotification('تم حفظ إعدادات المدينة وطريقة الحساب بنجاح', 'success');
                    } catch (error) {
                        console.error('خطأ في حفظ إعدادات المدينة وطريقة الحساب:', error);
                        showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
                    }
                });
            }

            // إعداد مستمع لزر اختبار حساب مواقيت الصلاة
            const testPrayerTimesBtn = document.getElementById('test-prayer-times');
            if (testPrayerTimesBtn) {
                testPrayerTimesBtn.addEventListener('click', async () => {
                    try {
                        // الحصول على القيم المحددة
                        const selectedCity = citySelect?.value || 'Asia/Amman';
                        const selectedMethod = methodSelect?.value || 'MWL';
                        const selectedJuristicMethod = juristicMethodSelect?.value || 'Shafi';

                        // حفظ القيم في التخزين المحلي
                        localStorage.setItem('selectedCity', selectedCity);
                        localStorage.setItem('calculationMethod', selectedMethod);
                        localStorage.setItem('juristicMethod', selectedJuristicMethod);

                        // تعيين طريقة الحساب والمذهب في مكتبة PrayTimes.js
                        if (window.prayTimes) {
                            // تعيين طريقة الحساب
                            window.prayTimes.setMethod(selectedMethod);

                            // تعيين المذهب (للعصر)
                            const juristicMethodValue = selectedJuristicMethod === 'Hanafi' ? 1 : 0;
                            window.prayTimes.adjust({asr: juristicMethodValue});
                        }

                        // عرض إشعار
                        showNotification('جاري حساب مواقيت الصلاة...', 'info');

                        // جلب أوقات الصلاة بالإعدادات الجديدة
                        const times = await getPrayerTimes();

                        // عرض المواقيت في إشعار
                        const cityName = citySelect?.options[citySelect?.selectedIndex]?.text || selectedCity;
                        const methodName = methodSelect?.options[methodSelect?.selectedIndex]?.text || selectedMethod;
                        const juristicMethodName = juristicMethodSelect?.options[juristicMethodSelect?.selectedIndex]?.text || selectedJuristicMethod;

                        // إنشاء نص المواقيت
                        const timesText = `
                            الفجر: ${times.fajr}
                            الشروق: ${times.sunrise}
                            الظهر: ${times.dhuhr}
                            العصر: ${times.asr}
                            المغرب: ${times.maghrib}
                            العشاء: ${times.isha}
                        `;

                        // عرض إشعار بالمواقيت
                        showNotification(`مواقيت الصلاة لمدينة ${cityName} بطريقة ${methodName} ومذهب ${juristicMethodName}:\n${timesText}`, 'success', 10000);
                    } catch (error) {
                        console.error('خطأ في اختبار حساب مواقيت الصلاة:', error);
                        showNotification('حدث خطأ أثناء حساب مواقيت الصلاة', 'error');
                    }
                });
            }
        }

        // إضافة دالة تحديث التاريخ
        function updateDate() {
            const dateDisplay = document.getElementById('dateDisplay');
            const now = new Date();

            // عرض التاريخ الميلادي
            const optionsGregorian = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            const gregorianDate = now.toLocaleDateString('ar-SA', optionsGregorian);

            // حساب التاريخ الهجري
            let hijriDate = new Intl.DateTimeFormat('ar-SA-islamic-umalqura', {
                weekday: 'long',
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            }).format(now);

            // تنسيق العرض مع إضافة الاختصارات
            dateDisplay.innerHTML = `<span style="color: #40E0D0">${gregorianDate} م</span><br><span style="color: #40E0D0">${hijriDate} هـ</span>`;
        }

        function getPrayerTimes() {
            console.log('بدء حساب مواقيت الصلاة...');

            try {
                // التحقق من وجود مواقيت معدلة يدوياً مفعلة
                const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                if (manualPrayerTimesEnabled && manualPrayerTimes && manualPrayerTimes[currentCity]) {
                    console.log('تم العثور على مواقيت معدلة يدوياً للمدينة:', currentCity);

                    // تطبيق المواقيت المعدلة يدوياً
                    const applyResult = applyManualPrayerTimes();

                    if (applyResult) {
                        console.log('تم تطبيق المواقيت المعدلة يدوياً بنجاح');
                        return manualPrayerTimes[currentCity];
                    } else {
                        console.warn('فشل تطبيق المواقيت المعدلة يدوياً، سيتم حساب المواقيت');
                    }
                }

                // التأكد من وجود مكتبة PrayTimes.js
                if (typeof prayTimes === 'undefined') {
                    console.error('مكتبة PrayTimes.js غير متوفرة');
                    throw new Error('مكتبة PrayTimes.js غير متوفرة');
                }

                // تعيين المتغير العالمي
                window.prayTimes = prayTimes;

                console.log('المدينة المختارة:', currentCity);

                // الحصول على طريقة الحساب المحفوظة
                const savedMethod = localStorage.getItem('calculationMethod') || 'MWL';
                console.log('طريقة الحساب المختارة:', savedMethod);

                // الحصول على المذهب المحفوظ
                const savedJuristicMethod = localStorage.getItem('juristicMethod') || 'Shafi';
                console.log('المذهب المختار:', savedJuristicMethod);

                // تعيين طريقة الحساب
                window.prayTimes.setMethod(savedMethod);

                // تعيين المذهب (للعصر)
                const juristicMethodValue = savedJuristicMethod === 'Hanafi' ? 1 : 0;
                window.prayTimes.adjust({asr: juristicMethodValue});

                // الحصول على إحداثيات المدينة
                const coordinates = getCityCoordinates(currentCity);
                if (!coordinates || !coordinates.lat || !coordinates.lng) {
                    console.error('إحداثيات غير صالحة للمدينة:', currentCity);
                    throw new Error('إحداثيات غير صالحة للمدينة: ' + currentCity);
                }
                console.log('إحداثيات المدينة:', coordinates);

                // الحصول على المنطقة الزمنية
                const timezone = getTimezone(currentCity);
                if (timezone === undefined) {
                    console.error('منطقة زمنية غير صالحة للمدينة:', currentCity);
                    throw new Error('منطقة زمنية غير صالحة للمدينة: ' + currentCity);
                }
                console.log('المنطقة الزمنية:', timezone);

                // الحصول على التاريخ الحالي
                const now = new Date();
                console.log('التاريخ الحالي:', now);

                // حساب مواقيت الصلاة
                const calculatedTimes = window.prayTimes.getTimes(
                    now,
                    [coordinates.lat, coordinates.lng],
                    timezone
                );

                if (!calculatedTimes) {
                    console.error('فشل في حساب مواقيت الصلاة للمدينة:', currentCity);
                    throw new Error('فشل في حساب مواقيت الصلاة للمدينة: ' + currentCity);
                }

                console.log('تم حساب المواقيت باستخدام مكتبة PrayTimes.js:', calculatedTimes);

                // تنسيق المواقيت
                const times = {
                    fajr: calculatedTimes.fajr,
                    sunrise: calculatedTimes.sunrise,
                    dhuhr: calculatedTimes.dhuhr,
                    asr: calculatedTimes.asr,
                    maghrib: calculatedTimes.maghrib,
                    isha: calculatedTimes.isha
                };

                // التحقق من صحة المواقيت
                let isValid = true;
                for (const prayer in times) {
                    if (!times[prayer] || times[prayer] === '--:--') {
                        console.error(`وقت ${prayer} غير صالح للمدينة ${currentCity}: ${times[prayer]}`);
                        isValid = false;
                        break;
                    }
                }

                if (!isValid) {
                    console.error('مواقيت غير صالحة للمدينة:', currentCity, times);
                    throw new Error('مواقيت غير صالحة للمدينة: ' + currentCity);
                }

                console.log('المواقيت المنسقة:', times);

                // تهيئة المتغير العالمي إذا لم يكن موجوداً
                if (!window.prayerTimes) {
                    window.prayerTimes = {};
                }

                // حذف المواقيت القديمة
                delete window.prayerTimes[currentCity];

                // تحديث المواقيت العالمية للمدينة المحددة
                window.prayerTimes[currentCity] = { ...times };

                // تحديث المواقيت الثابتة أيضاً
                if (!window.AMMAN_PRAYER_TIMES) {
                    window.AMMAN_PRAYER_TIMES = {};
                }
                window.AMMAN_PRAYER_TIMES[currentCity] = { ...times };

                console.log('تم تحديث مواقيت الصلاة للمدينة:', currentCity, window.prayerTimes[currentCity]);

                // تحديث العرض
                updatePrayerTimesDisplay(times, '24');

                // تحديث جميع عروض مواقيت الصلاة في التطبيق
                updateAllPrayerTimesInApp();

                // تحديث حقول التعديل اليدوي
                updateManualPrayerTimeInputs();

                // عرض إشعار بتحديث المواقيت
                const cityName = document.getElementById('selected-city')?.options[document.getElementById('selected-city')?.selectedIndex]?.text || currentCity;
                const methodName = document.getElementById('calculation-method')?.options[document.getElementById('calculation-method')?.selectedIndex]?.text || savedMethod;
                showNotification(`تم تحديث مواقيت الصلاة لمدينة ${cityName} بطريقة ${methodName}`, 'info');

                return times;
            } catch (error) {
                console.error('خطأ في حساب مواقيت الصلاة:', error);
                showNotification('حدث خطأ أثناء حساب مواقيت الصلاة', 'error');

                // محاولة استخدام المواقيت الثابتة كاحتياطي
                try {
                    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

                    // إنشاء مواقيت افتراضية إذا لم تكن موجودة
                    if (!window.AMMAN_PRAYER_TIMES) {
                        window.AMMAN_PRAYER_TIMES = {
                            'Asia/Amman': {
                                fajr: '04:00',
                                sunrise: '05:30',
                                dhuhr: '12:00',
                                asr: '15:30',
                                maghrib: '18:30',
                                isha: '20:00'
                            }
                        };
                    }

                    const defaultTimes = window.AMMAN_PRAYER_TIMES[currentCity] || window.AMMAN_PRAYER_TIMES['Asia/Amman'];

                    if (!defaultTimes) {
                        throw new Error('لا توجد مواقيت ثابتة للمدينة المحددة');
                    }

                    console.log('استخدام المواقيت الثابتة كاحتياطي:', defaultTimes);

                    // تهيئة المتغير العالمي إذا لم يكن موجوداً
                    if (!window.prayerTimes) {
                        window.prayerTimes = {};
                    }

                    // تحديث المواقيت العالمية
                    window.prayerTimes[currentCity] = { ...defaultTimes };

                    // تحديث العرض
                    updatePrayerTimesDisplay(defaultTimes, '24');

                    // تحديث جميع عروض مواقيت الصلاة في التطبيق
                    updateAllPrayerTimesInApp();

                    // تحديث حقول التعديل اليدوي
                    updateManualPrayerTimeInputs();

                    return defaultTimes;
                } catch (fallbackError) {
                    console.error('خطأ في استخدام المواقيت الثابتة:', fallbackError);

                    // إنشاء مواقيت افتراضية كملاذ أخير
                    const emergencyTimes = {
                        fajr: '04:00',
                        sunrise: '05:30',
                        dhuhr: '12:00',
                        asr: '15:30',
                        maghrib: '18:30',
                        isha: '20:00'
                    };

                    console.warn('استخدام مواقيت افتراضية كملاذ أخير:', emergencyTimes);

                    // تحديث المواقيت العالمية
                    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                    if (!window.prayerTimes) {
                        window.prayerTimes = {};
                    }
                    window.prayerTimes[currentCity] = { ...emergencyTimes };

                    // تحديث العرض
                    updatePrayerTimesDisplay(emergencyTimes, '24');

                    // تحديث حقول التعديل اليدوي
                    updateManualPrayerTimeInputs();

                    return emergencyTimes;
                }
            }
        }

        // دالة للحصول على إحداثيات المدينة
        function getCityCoordinates(city) {
            // قاعدة بيانات الإحداثيات للمدن
            const cityCoordinates = {
                'Asia/Amman': { lat: 31.9454, lng: 35.9284, elv: 0 },      // عمان: خط العرض، خط الطول، الارتفاع
                'Asia/Riyadh': { lat: 24.7136, lng: 46.6753, elv: 0 },     // الرياض
                'Asia/Dubai': { lat: 25.2048, lng: 55.2708, elv: 0 },      // دبي
                'Asia/Makkah': { lat: 21.4225, lng: 39.8262, elv: 0 },     // مكة المكرمة
                'Asia/Madinah': { lat: 24.5247, lng: 39.5692, elv: 0 },    // المدينة المنورة
                'Asia/Jerusalem': { lat: 31.7683, lng: 35.2137, elv: 0 },  // القدس
                'Asia/Baghdad': { lat: 33.3152, lng: 44.3661, elv: 0 },    // بغداد
                'Africa/Cairo': { lat: 30.0444, lng: 31.2357, elv: 0 },    // القاهرة
                'Africa/Tunis': { lat: 36.8065, lng: 10.1815, elv: 0 },    // تونس
                'Africa/Algiers': { lat: 36.7538, lng: 3.0588, elv: 0 },   // الجزائر
                'Africa/Casablanca': { lat: 33.5731, lng: -7.5898, elv: 0 }, // الدار البيضاء
                'Europe/Istanbul': { lat: 41.0082, lng: 28.9784, elv: 0 }, // إسطنبول
                'Asia/Tehran': { lat: 35.6892, lng: 51.3890, elv: 0 },     // طهران
                'Asia/Karachi': { lat: 24.8607, lng: 67.0011, elv: 0 },    // كراتشي
                'Asia/Kuala_Lumpur': { lat: 3.1390, lng: 101.6869, elv: 0 }, // كوالالمبور
                'Asia/Jakarta': { lat: -6.2088, lng: 106.8456, elv: 0 }    // جاكرتا
            };

            // إذا كانت المدينة غير موجودة، استخدم عمان كقيمة افتراضية
            return cityCoordinates[city] || cityCoordinates['Asia/Amman'];
        }

        // دالة للحصول على المنطقة الزمنية
        function getTimezone(city) {
            // قاعدة بيانات المناطق الزمنية للمدن
            const timezones = {
                'Asia/Amman': 3,        // عمان: GMT+3
                'Asia/Riyadh': 3,       // الرياض: GMT+3
                'Asia/Dubai': 4,        // دبي: GMT+4
                'Asia/Makkah': 3,       // مكة المكرمة: GMT+3
                'Asia/Madinah': 3,      // المدينة المنورة: GMT+3
                'Asia/Jerusalem': 2,    // القدس: GMT+2
                'Asia/Baghdad': 3,      // بغداد: GMT+3
                'Africa/Cairo': 2,      // القاهرة: GMT+2
                'Africa/Tunis': 1,      // تونس: GMT+1
                'Africa/Algiers': 1,    // الجزائر: GMT+1
                'Africa/Casablanca': 1, // الدار البيضاء: GMT+1
                'Europe/Istanbul': 3,   // إسطنبول: GMT+3
                'Asia/Tehran': 3.5,     // طهران: GMT+3.5
                'Asia/Karachi': 5,      // كراتشي: GMT+5
                'Asia/Kuala_Lumpur': 8, // كوالالمبور: GMT+8
                'Asia/Jakarta': 7       // جاكرتا: GMT+7
            };

            // إذا كانت المدينة غير موجودة، استخدم عمان كقيمة افتراضية
            return timezones[city] || 3;
        }

        // دالة لتحديث مواقيت الصلاة بشكل إجباري
        function forceUpdatePrayerTimes() {
            console.log('بدء التحديث الإجباري لمواقيت الصلاة...');

            try {
                // التحقق من وجود تعديلات يدوية مفعلة
                const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                if (manualPrayerTimesEnabled && manualPrayerTimes[currentCity]) {
                    console.log('تم العثور على تعديلات يدوية مفعلة للمدينة:', currentCity);
                    console.log('سيتم استخدام المواقيت المعدلة يدوياً بدلاً من إعادة الحساب');

                    // تطبيق المواقيت المعدلة يدوياً
                    const applyResult = applyManualPrayerTimes();

                    // تحديث حقول التعديل اليدوي
                    updateManualPrayerTimeInputs();

                    console.log('تم تطبيق المواقيت المعدلة يدوياً بنجاح');

                    // تأخير عرض الإشعار لضمان تطبيق المواقيت أولاً
                    setTimeout(() => {
                        showNotification('تم تطبيق المواقيت المعدلة يدوياً بنجاح', 'success');
                    }, 500);

                    return manualPrayerTimes[currentCity];
                }

                // التأكد من وجود مكتبة PrayTimes.js
                if (typeof prayTimes === 'undefined') {
                    console.error('مكتبة PrayTimes.js غير متوفرة');
                    alert('مكتبة PrayTimes.js غير متوفرة');
                    return null;
                }

                // تعيين المتغير العالمي
                window.prayTimes = prayTimes;

                // الحصول على القيم المحددة
                const selectedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                const selectedMethod = localStorage.getItem('calculationMethod') || 'MWL';
                const selectedJuristicMethod = localStorage.getItem('juristicMethod') || 'Shafi';

                console.log('المدينة المختارة:', selectedCity);
                console.log('طريقة الحساب المختارة:', selectedMethod);
                console.log('المذهب المختار:', selectedJuristicMethod);

                // تعيين طريقة الحساب
                window.prayTimes.setMethod(selectedMethod);

                // تعيين المذهب (للعصر)
                const juristicMethodValue = selectedJuristicMethod === 'Hanafi' ? 1 : 0;
                window.prayTimes.adjust({asr: juristicMethodValue});

                // الحصول على إحداثيات المدينة
                const coordinates = getCityCoordinates(selectedCity);
                console.log('إحداثيات المدينة:', coordinates);

                // الحصول على المنطقة الزمنية
                const timezone = getTimezone(selectedCity);
                console.log('المنطقة الزمنية:', timezone);

                // الحصول على التاريخ الحالي
                const now = new Date();
                console.log('التاريخ الحالي:', now);

                // حساب مواقيت الصلاة
                const calculatedTimes = window.prayTimes.getTimes(
                    now,
                    [coordinates.lat, coordinates.lng],
                    timezone
                );

                console.log('تم حساب المواقيت باستخدام مكتبة PrayTimes.js:', calculatedTimes);

                // تنسيق المواقيت
                const times = {
                    fajr: calculatedTimes.fajr,
                    sunrise: calculatedTimes.sunrise,
                    dhuhr: calculatedTimes.dhuhr,
                    asr: calculatedTimes.asr,
                    maghrib: calculatedTimes.maghrib,
                    isha: calculatedTimes.isha
                };

                console.log('المواقيت المنسقة:', times);

                // تهيئة المتغيرات العالمية
                window.prayerTimes = {};
                window.AMMAN_PRAYER_TIMES = {};

                // تحديث المواقيت العالمية للمدينة المحددة
                window.prayerTimes[selectedCity] = times;
                window.AMMAN_PRAYER_TIMES[selectedCity] = times;

                // إنشاء مواقيت ثابتة جديدة لجميع المدن
                const cities = [
                    'Asia/Amman', 'Asia/Riyadh', 'Asia/Dubai', 'Asia/Makkah', 'Asia/Madinah',
                    'Asia/Jerusalem', 'Asia/Baghdad', 'Africa/Cairo', 'Africa/Tunis',
                    'Africa/Algiers', 'Africa/Casablanca', 'Asia/Istanbul', 'Asia/Tehran',
                    'Asia/Karachi', 'Asia/Kuala_Lumpur', 'Asia/Jakarta'
                ];

                console.log('بدء حساب مواقيت الصلاة لجميع المدن...');

                // حساب مواقيت الصلاة لجميع المدن
                for (const city of cities) {
                    if (city === selectedCity) continue; // تخطي المدينة المحددة لأننا قمنا بحسابها بالفعل

                    try {
                        // التحقق من وجود مواقيت معدلة يدوياً للمدينة
                        if (manualPrayerTimesEnabled && manualPrayerTimes && manualPrayerTimes[city]) {
                            console.log(`استخدام المواقيت المعدلة يدوياً لمدينة ${city}`);

                            // تحديث المواقيت العالمية بالمواقيت المعدلة يدوياً
                            window.prayerTimes[city] = { ...manualPrayerTimes[city] };
                            window.AMMAN_PRAYER_TIMES[city] = { ...manualPrayerTimes[city] };

                            continue; // الانتقال إلى المدينة التالية
                        }

                        // الحصول على إحداثيات المدينة
                        const cityCoordinates = getCityCoordinates(city);
                        if (!cityCoordinates || !cityCoordinates.lat || !cityCoordinates.lng) {
                            console.error(`إحداثيات غير صالحة لمدينة ${city}:`, cityCoordinates);
                            continue;
                        }

                        // الحصول على المنطقة الزمنية
                        const cityTimezone = getTimezone(city);
                        if (cityTimezone === undefined) {
                            console.error(`منطقة زمنية غير صالحة لمدينة ${city}`);
                            continue;
                        }

                        // حساب مواقيت الصلاة
                        const cityTimes = window.prayTimes.getTimes(
                            now,
                            [cityCoordinates.lat, cityCoordinates.lng],
                            cityTimezone
                        );

                        if (!cityTimes) {
                            console.error(`فشل في حساب مواقيت الصلاة لمدينة ${city}`);
                            continue;
                        }

                        // تنسيق المواقيت
                        const formattedTimes = {
                            fajr: cityTimes.fajr,
                            sunrise: cityTimes.sunrise,
                            dhuhr: cityTimes.dhuhr,
                            asr: cityTimes.asr,
                            maghrib: cityTimes.maghrib,
                            isha: cityTimes.isha
                        };

                        // التحقق من صحة المواقيت
                        let isValid = true;
                        for (const prayer in formattedTimes) {
                            if (!formattedTimes[prayer] || formattedTimes[prayer] === '--:--') {
                                console.error(`وقت ${prayer} غير صالح لمدينة ${city}: ${formattedTimes[prayer]}`);
                                isValid = false;
                                break;
                            }
                        }

                        if (!isValid) {
                            console.error(`مواقيت غير صالحة لمدينة ${city}:`, formattedTimes);
                            continue;
                        }

                        // تحديث المواقيت العالمية
                        window.prayerTimes[city] = formattedTimes;
                        window.AMMAN_PRAYER_TIMES[city] = formattedTimes;

                        console.log(`تم حساب مواقيت الصلاة لمدينة ${city}:`, formattedTimes);
                    } catch (error) {
                        console.error(`خطأ في حساب مواقيت الصلاة لمدينة ${city}:`, error);
                    }
                }

                console.log('تم الانتهاء من حساب مواقيت الصلاة لجميع المدن');

                // تحديث العرض مباشرة
                updatePrayerTimesDisplay(times, '24');

                // تحديث جميع عروض مواقيت الصلاة في التطبيق
                updateAllPrayerTimesInApp();

                // تحديث حقول التعديل اليدوي
                updateManualPrayerTimeInputs();

                console.log('تم التحديث الإجباري لمواقيت الصلاة بنجاح');
                alert('تم التحديث الإجباري لمواقيت الصلاة بنجاح');

                return times;
            } catch (error) {
                console.error('خطأ في التحديث الإجباري لمواقيت الصلاة:', error);
                alert('حدث خطأ أثناء التحديث الإجباري لمواقيت الصلاة');

                // محاولة استخدام المواقيت الثابتة كاحتياطي
                try {
                    const selectedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

                    // إنشاء مواقيت افتراضية إذا لم تكن موجودة
                    if (!window.AMMAN_PRAYER_TIMES) {
                        window.AMMAN_PRAYER_TIMES = {
                            'Asia/Amman': {
                                fajr: '04:00',
                                sunrise: '05:30',
                                dhuhr: '12:00',
                                asr: '15:30',
                                maghrib: '18:30',
                                isha: '20:00'
                            }
                        };
                    }

                    const defaultTimes = window.AMMAN_PRAYER_TIMES[selectedCity] || window.AMMAN_PRAYER_TIMES['Asia/Amman'];

                    if (!defaultTimes) {
                        throw new Error('لا توجد مواقيت ثابتة للمدينة المحددة');
                    }

                    console.log('استخدام المواقيت الثابتة كاحتياطي:', defaultTimes);

                    return defaultTimes;
                } catch (fallbackError) {
                    console.error('خطأ في استخدام المواقيت الثابتة:', fallbackError);
                    return null;
                }
            }
        }

        // دالة لتحديث جميع عروض مواقيت الصلاة في التطبيق
        function updateAllPrayerTimesDisplays() {
            try {
                // الحصول على المدينة المحددة
                const selectedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

                // الحصول على المواقيت
                const times = window.prayerTimes[selectedCity];

                if (!times) {
                    console.error('لا توجد مواقيت للمدينة المحددة');
                    return;
                }

                // تحديث عرض المواقيت في المستطيل الأفقي
                updatePrayerTimesDisplay(times, '24');

                // تحديث قائمة الصلوات القادمة
                updateUpcomingPrayers();

                // تحديث العد التنازلي
                updateCountdown();

                // تحديث نص الصلاة القادمة
                updateNextPrayerText();

                console.log('تم تحديث جميع عروض مواقيت الصلاة بنجاح');
            } catch (error) {
                console.error('خطأ في تحديث عروض مواقيت الصلاة:', error);
            }
        }

        function updatePrayerTimesDisplay(times, timeFormat) {
            const formatTime = (time) => {
                if (!time) return '--:--';

                let [hours, minutes] = time.split(':').map(Number);

                if (timeFormat === '12') {
                    const period = hours >= 12 ? 'م' : 'ص';
                    hours = hours % 12 || 12;
                    return `<span style="color: #40E0D0">${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}</span> ${period}`;
                }
                return `<span style="color: #40E0D0">${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}</span>`;
            };

            console.log('تحديث عرض مواقيت الصلاة:', times);

            // التحقق من صحة البيانات
            if (!times || typeof times !== 'object') {
                console.error('بيانات مواقيت الصلاة غير صالحة:', times);

                // استخدام المواقيت الثابتة كاحتياطي
                const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                times = window.AMMAN_PRAYER_TIMES[currentCity] || window.AMMAN_PRAYER_TIMES['Asia/Amman'];

                if (!times) {
                    console.error('لا يمكن استخدام المواقيت الثابتة كاحتياطي');
                    return;
                }

                console.log('تم استخدام المواقيت الثابتة كاحتياطي:', times);
            }

            // تحديث عرض المواقيت
            const fajrTime = document.getElementById('fajr-time');
            const sunriseTime = document.getElementById('sunrise-time');
            const dhuhrTime = document.getElementById('dhuhr-time');
            const asrTime = document.getElementById('asr-time');
            const maghribTime = document.getElementById('maghrib-time');
            const ishaTime = document.getElementById('isha-time');

            // التحقق من وجود عناصر المواقيت
            if (!fajrTime || !sunriseTime || !dhuhrTime || !asrTime || !maghribTime || !ishaTime) {
                console.error('عناصر مواقيت الصلاة غير موجودة، جاري إنشاء المستطيل الأفقي...');

                // إنشاء المستطيل الأفقي إذا لم يكن موجوداً
                createPrayerTimesBar();

                // إعادة الحصول على العناصر بعد إنشائها
                const fajrTime = document.getElementById('fajr-time');
                const sunriseTime = document.getElementById('sunrise-time');
                const dhuhrTime = document.getElementById('dhuhr-time');
                const asrTime = document.getElementById('asr-time');
                const maghribTime = document.getElementById('maghrib-time');
                const ishaTime = document.getElementById('isha-time');

                if (!fajrTime || !sunriseTime || !dhuhrTime || !asrTime || !maghribTime || !ishaTime) {
                    console.error('فشل في إنشاء عناصر مواقيت الصلاة');
                    return;
                }
            }

            // تحديث المواقيت
            fajrTime.innerHTML = formatTime(times.fajr);
            sunriseTime.innerHTML = formatTime(times.sunrise);
            dhuhrTime.innerHTML = formatTime(times.dhuhr);
            asrTime.innerHTML = formatTime(times.asr);
            maghribTime.innerHTML = formatTime(times.maghrib);
            ishaTime.innerHTML = formatTime(times.isha);

            console.log('تم تحديث مواقيت الصلاة بنجاح');
        }

        // دالة لإنشاء المستطيل الأفقي لمواقيت الصلاة إذا لم يكن موجوداً
        function createPrayerTimesBar() {
            // التحقق من وجود المستطيل الأفقي
            let prayerTimesBar = document.querySelector('.prayer-times');

            if (!prayerTimesBar) {
                console.log('إنشاء المستطيل الأفقي لمواقيت الصلاة...');

                // إنشاء المستطيل الأفقي
                prayerTimesBar = document.createElement('div');
                prayerTimesBar.className = 'prayer-times';

                // تعيين أنماط CSS مباشرة للتأكد من ظهور المستطيل الأفقي
                prayerTimesBar.style.width = 'calc(100% - 5cm)';
                prayerTimesBar.style.height = '3cm';
                prayerTimesBar.style.backgroundColor = '#800020'; // لون داكن وردي
                prayerTimesBar.style.color = '#D4AF37'; // لون ذهبي
                prayerTimesBar.style.display = 'flex';
                prayerTimesBar.style.justifyContent = 'space-around';
                prayerTimesBar.style.alignItems = 'center';
                prayerTimesBar.style.padding = '10px 0';
                prayerTimesBar.style.position = 'fixed';
                prayerTimesBar.style.bottom = '0';
                prayerTimesBar.style.left = '0';
                prayerTimesBar.style.zIndex = '9999'; // زيادة قيمة z-index للتأكد من ظهوره فوق العناصر الأخرى
                prayerTimesBar.style.boxShadow = '0 -2px 5px rgba(0, 0, 0, 0.5)';
                prayerTimesBar.style.border = '2px solid #D4AF37'; // إضافة حدود ذهبية

                // إنشاء عناصر مواقيت الصلاة
                const prayers = [
                    { name: 'الفجر', id: 'fajr-time' },
                    { name: 'الشروق', id: 'sunrise-time' },
                    { name: 'الظهر', id: 'dhuhr-time' },
                    { name: 'العصر', id: 'asr-time' },
                    { name: 'المغرب', id: 'maghrib-time' },
                    { name: 'العشاء', id: 'isha-time' }
                ];

                // إضافة عناصر مواقيت الصلاة إلى المستطيل الأفقي
                prayers.forEach(prayer => {
                    const prayerTime = document.createElement('div');
                    prayerTime.className = 'prayer-time';
                    prayerTime.style.textAlign = 'center';
                    prayerTime.style.padding = '10px';

                    const prayerName = document.createElement('div');
                    prayerName.className = 'prayer-name';
                    prayerName.textContent = prayer.name;
                    prayerName.style.fontSize = '1.8em';
                    prayerName.style.fontWeight = 'bold';
                    prayerName.style.marginBottom = '10px';
                    prayerName.style.color = '#40E0D0';

                    const prayerHour = document.createElement('div');
                    prayerHour.className = 'prayer-hour';
                    prayerHour.id = prayer.id;
                    prayerHour.innerHTML = '--:--';
                    prayerHour.style.fontSize = '1.7em';
                    prayerHour.style.marginTop = '10px';
                    prayerHour.style.color = '#40E0D0';

                    prayerTime.appendChild(prayerName);
                    prayerTime.appendChild(prayerHour);
                    prayerTimesBar.appendChild(prayerTime);
                });

                // إضافة المستطيل الأفقي إلى الصفحة
                document.body.appendChild(prayerTimesBar);

                console.log('تم إنشاء المستطيل الأفقي لمواقيت الصلاة بنجاح');
            }

            // تحديث مواقيت الصلاة بعد إنشاء المستطيل (سواء كان موجوداً أم تم إنشاؤه)
            setTimeout(function() {
                // استخدام المواقيت الثابتة كاحتياطي أولي
                const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                const defaultTimes = window.AMMAN_PRAYER_TIMES[currentCity] || window.AMMAN_PRAYER_TIMES['Asia/Amman'];

                // تحديث العرض بالمواقيت الثابتة
                updatePrayerTimesDisplay(defaultTimes, '24');

                // ثم محاولة تحديث المواقيت من الحساب الفعلي
                updatePrayerTimes();
            }, 500);

            return prayerTimesBar;
        }

        function displayRemainingPrayerTimes() {
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            const times = window.prayerTimes?.[currentCity];

            if (!times) {
                console.warn('أوقات الصلوات غير متوفرة');
                return;
            }

            // تحديث العرض للصلاة القادمة
            const now = new Date();
            const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

            const prayers = [
                { name: 'الفجر', time: times.fajr },
                { name: 'الشروق', time: times.sunrise },
                { name: 'الظهر', time: times.dhuhr },
                { name: 'العصر', time: times.asr },
                { name: 'المغرب', time: times.maghrib },
                { name: 'العشاء', time: times.isha }
            ];

            const nextPrayer = prayers.find(prayer => prayer.time > currentTime) || prayers[0];
            const nextPrayerText = document.querySelector('.next-prayer-text');
            if (nextPrayerText) {
                nextPrayerText.textContent = `ننتظر صلاة ${nextPrayer.name}`;
            }
        }

        // تعديل دالة startCountdownTimer لتحديث قائمة الصلوات القادمة أيضًا
        function startCountdownTimer() {
            // تحديث العد التنازلي مباشرة
            updateCountdown();

            // التحقق ما إذا كان وقت الصلاة قد دخل وتشغيل الأذان
            checkPrayerTimeAndPlayAdhan();

            // تحديث قائمة الصلوات القادمة
            updateUpcomingPrayers();

            // ثم تحديثه كل دقيقة مع التحقق من وقت الصلاة
            setInterval(() => {
                updateCountdown();
                checkPrayerTimeAndPlayAdhan();
                updateUpcomingPrayers();
                updateNextPrayerText(); // تحديث نص الصلاة القادمة
            }, 60000);
        }

        // دالة لتحديث العد التنازلي للصلاة القادمة
        function updateCountdown() {
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            const times = window.prayerTimes?.[currentCity];

            if (!times) {
                console.log('أوقات الصلوات غير متوفرة للعد التنازلي');
                return;
            }

            const now = new Date();
            const currentHours = now.getHours();
            const currentMinutes = now.getMinutes();
            const currentTimeInMinutes = currentHours * 60 + currentMinutes;

            // ترتيب الصلوات
            const prayers = [
                { name: 'fajr', arabicName: 'الفجر', time: times.fajr, element: 'fajr-countdown' },
                { name: 'sunrise', arabicName: 'الشروق', time: times.sunrise, element: 'sunrise-countdown' },
                { name: 'dhuhr', arabicName: 'الظهر', time: times.dhuhr, element: 'dhuhr-countdown' },
                { name: 'asr', arabicName: 'العصر', time: times.asr, element: 'asr-countdown' },
                { name: 'maghrib', arabicName: 'المغرب', time: times.maghrib, element: 'maghrib-countdown' },
                { name: 'isha', arabicName: 'العشاء', time: times.isha, element: 'isha-countdown' }
            ];

            // تحويل وقت الصلاة إلى دقائق للمقارنة
            prayers.forEach(prayer => {
                if (prayer.time) {
                    const [hours, minutes] = prayer.time.split(':').map(Number);
                    prayer.timeInMinutes = hours * 60 + minutes;
                } else {
                    prayer.timeInMinutes = -1; // قيمة غير صالحة إذا كان الوقت غير متاح
                }
            });

            // البحث عن الصلاة القادمة
            let nextPrayer = null;
            let minTimeToNextPrayer = Infinity;

            // حساب الوقت المتبقي لكل صلاة
            for (const prayer of prayers) {
                if (!prayer.time) continue;

                // حساب الوقت المتبقي للصلاة
                let timeToNextPrayer = prayer.timeInMinutes - currentTimeInMinutes;

                // إذا كان الوقت سالباً (أي أن الصلاة قد مرت)، أضف 24 ساعة
                if (timeToNextPrayer < 0) {
                    timeToNextPrayer += 24 * 60; // 24 ساعة بالدقائق
                }

                // إذا كان هذا أقرب صلاة قادمة
                if (timeToNextPrayer < minTimeToNextPrayer) {
                    minTimeToNextPrayer = timeToNextPrayer;
                    nextPrayer = prayer;
                }
            }

            // التأكد من وجود صلاة قادمة
            if (!nextPrayer) {
                console.error('لم يتم العثور على صلاة قادمة!');
                nextPrayer = prayers[0]; // استخدام الفجر كاحتياطي
            }

            // حساب الوقت المتبقي
            let remainingMinutes = nextPrayer.timeInMinutes - currentTimeInMinutes;
            if (remainingMinutes < 0) {
                remainingMinutes += 1440; // إضافة 24 ساعة إذا كان الوقت سالبًا
            }

            const remainingHours = Math.floor(remainingMinutes / 60);
            const minutes = remainingMinutes % 60;

            // تنسيق العرض
            const formattedTime = `<span style="color: #40E0D0; font-weight: 900; font-size: 1.2em; text-shadow: 0 0 3px rgba(64, 224, 208, 0.5);">${String(remainingHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}</span>`;

            // تحديث عرض الوقت المتبقي للصلاة القادمة
            const nextPrayerElement = document.querySelector('.next-prayer-countdown');
            if (nextPrayerElement) {
                nextPrayerElement.innerHTML = formattedTime;
            }

            // تحديث عرض الصلاة القادمة
            const nextPrayerText = document.querySelector('.next-prayer-text');
            if (nextPrayerText) {
                // تحديث نص الصلاة القادمة ليعرض "ننتظر صلاة X"
                nextPrayerText.innerHTML = `ننتظر صلاة ${nextPrayer.arabicName}`;
            }

            // تحديث العد التنازلي لكل صلاة
            prayers.forEach(prayer => {
                const countdownElement = document.getElementById(prayer.element);
                if (countdownElement) {
                    let prayerRemainingMinutes = prayer.timeInMinutes - currentTimeInMinutes;
                    if (prayerRemainingMinutes < 0) {
                        prayerRemainingMinutes += 1440; // إضافة 24 ساعة إذا كان الوقت سالبًا
                    }

                    const prayerRemainingHours = Math.floor(prayerRemainingMinutes / 60);
                    const prayerRemainingMins = prayerRemainingMinutes % 60;

                    // تحديث العرض بلون مختلف للصلاة القادمة
                    if (prayer.name === nextPrayer.name) {
                        countdownElement.innerHTML = `<span style="color: #40E0D0; font-weight: 900; text-shadow: 0 0 2px rgba(64, 224, 208, 0.5);">${String(prayerRemainingHours).padStart(2, '0')}:${String(prayerRemainingMins).padStart(2, '0')}</span>`;
                    } else {
                        countdownElement.innerHTML = `<span style="font-weight: 700;">${String(prayerRemainingHours).padStart(2, '0')}:${String(prayerRemainingMins).padStart(2, '0')}</span>`;
                    }
                }
            });

            return nextPrayer;
        }

        // دالة لفحص ما إذا كان وقت الصلاة قد دخل وتشغيل الأذان
        function checkPrayerTimeAndPlayAdhan() {
            // تقليل رسائل وحدة التحكم
            // console.log('جاري التحقق من وقت الصلاة...');

            // التحقق ما إذا كان الأذان مفعلاً من الإعدادات المحفوظة
            let enableAdhan = false;

            // التحقق ما إذا كان التعتيم مفعلاً
            let enableDarkness = true;

            // محاولة قراءة إعدادات التعتيم
            try {
                const darknessSettings = JSON.parse(localStorage.getItem('darknessSettings') || '{}');
                enableDarkness = darknessSettings.enabled !== undefined ? darknessSettings.enabled : true;
            } catch (error) {
                console.error('خطأ في قراءة إعدادات التعتيم:', error);
                // استخدام القيمة الافتراضية
                enableDarkness = true;
            }

            // تحديث المتغير العالمي
            window.enableDarkness = enableDarkness;

            // محاولة قراءة إعدادات الأذان من التخزين المحلي
            try {
                const adhanSettings = JSON.parse(localStorage.getItem('adhanSettings') || '{}');
                enableAdhan = adhanSettings.enabled !== undefined ? adhanSettings.enabled : document.getElementById('enable-adhan')?.checked || false;
            } catch (error) {
                console.error('خطأ في قراءة إعدادات الأذان:', error);
                // استخدام حالة عنصر التحكم كاحتياطي
                enableAdhan = document.getElementById('enable-adhan')?.checked || false;
            }

            // تقليل رسائل وحدة التحكم
            // console.log('حالة الأذان:', enableAdhan ? 'مفعل' : 'غير مفعل');

            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            const times = window.prayerTimes?.[currentCity];

            if (!times) {
                console.warn('أوقات الصلوات غير متوفرة للتحقق من دخول وقت الصلاة');
                return;
            }

            const now = new Date();
            const currentHours = now.getHours();
            const currentMinutes = now.getMinutes();
            const currentSeconds = now.getSeconds();
            const currentTime = `${String(currentHours).padStart(2, '0')}:${String(currentMinutes).padStart(2, '0')}`;
            // تقليل رسائل وحدة التحكم
            // console.log('الوقت الحالي:', currentTime);

            // التحقق من صحة مواقيت الصلاة
            if (!times.fajr || !times.dhuhr || !times.asr || !times.maghrib || !times.isha) {
                console.warn('بعض مواقيت الصلاة غير معرفة، استخدام المواقيت الثابتة...');

                // استخدام المواقيت الثابتة من window.AMMAN_PRAYER_TIMES
                const fixedTimes = window.AMMAN_PRAYER_TIMES[currentCity] || window.AMMAN_PRAYER_TIMES['Asia/Amman'];

                // تحديث المواقيت العالمية
                if (!window.prayerTimes) {
                    window.prayerTimes = {};
                }

                window.prayerTimes[currentCity] = {
                    fajr: fixedTimes.fajr || '03:55',
                    sunrise: fixedTimes.sunrise || '05:25',
                    dhuhr: fixedTimes.dhuhr || '12:35',
                    asr: fixedTimes.asr || '16:15',
                    maghrib: fixedTimes.maghrib || '19:45',
                    isha: fixedTimes.isha || '21:15'
                };

                // استخدام المواقيت المحدثة
                times = window.prayerTimes[currentCity];

                console.log('تم استخدام المواقيت الثابتة:', times);
            }

            // تحقق من كل وقت صلاة
            const prayers = [
                { name: 'fajr', arabicName: 'الفجر', time: times.fajr, sound: 'audio/audio_fajr.mp3' },
                { name: 'dhuhr', arabicName: 'الظهر', time: times.dhuhr, sound: 'audio/audio_dhar.mp3' },
                { name: 'asr', arabicName: 'العصر', time: times.asr, sound: 'audio/audio_dhar.mp3' },
                { name: 'maghrib', arabicName: 'المغرب', time: times.maghrib, sound: 'audio/audio_dhar.mp3' },
                { name: 'isha', arabicName: 'العشاء', time: times.isha, sound: 'audio/audio_dhar.mp3' }
            ];

            // تقليل رسائل وحدة التحكم
            // console.log('أوقات الصلوات اليوم:', prayers.map(p => `${p.arabicName}: ${p.time}`));

            for (const prayer of prayers) {
                // إذا كان الوقت الحالي يطابق بالضبط وقت الصلاة والثواني 0
                if (currentTime === prayer.time && currentSeconds === 0) {
                    console.log(`دخل وقت صلاة ${prayer.arabicName}، جاري تشغيل الأذان وبدء العد التنازلي للإقامة...`);

                    // التحقق من عدم تشغيل الأذان لهذه الصلاة اليوم
                    const today = new Date().toISOString().split('T')[0];
                    const adhanPlayedKey = `adhan_played_${today}_${prayer.name}`;
                    const adhanPlayed = localStorage.getItem(adhanPlayedKey);

                    if (!adhanPlayed) {
                        // بدء العد التنازلي للإقامة مباشرة
                        startIqamahCountdown(prayer.name, prayer.arabicName);

                        // تشغيل الأذان المناسب إذا كان مفعلاً
                        if (enableAdhan) {
                            const adhanAudio = document.getElementById('adhan-audio');
                            if (adhanAudio) {
                                adhanAudio.src = prayer.sound;
                                adhanAudio.currentTime = 0; // إعادة تعيين الصوت إلى البداية
                                adhanAudio.volume = parseFloat(document.getElementById('adhan-volume')?.value || 1);

                                // محاولة تشغيل الأذان
                                const playAdhan = () => {
                                    // محاولة تشغيل الأذان مع دعم الصوت البديل
                                    const playPromise = window.playAdhanFixed ?
                                        window.playAdhanFixed() :
                                        adhanAudio.play();

                                    playPromise
                                        .then(() => {
                                            console.log('✅ بدأ تشغيل الأذان بنجاح');
                                            // تخزين أن الأذان قد تم تشغيله لهذه الصلاة اليوم
                                            localStorage.setItem(adhanPlayedKey, 'true');
                                        })
                                        .catch(err => {
                                            console.error('❌ خطأ في تشغيل الأذان:', err);

                                            // محاولة تشغيل صوت بديل
                                            if (window.playAdhanFixed) {
                                                window.playAdhanFixed().then(() => {
                                                    console.log('✅ تم تشغيل الأذان البديل');
                                                    localStorage.setItem(adhanPlayedKey, 'true');
                                                }).catch(e => {
                                                    console.error('❌ فشل تشغيل الأذان البديل:', e);
                                                });
                                            }
                                            // إعادة المحاولة بعد ثانية إذا فشلت
                                            setTimeout(playAdhan, 1000);
                                        });
                                };

                                playAdhan();
                            } else {
                                console.error('عنصر صوت الأذان غير موجود');
                                localStorage.setItem(adhanPlayedKey, 'true');
                            }
                        } else {
                            // إذا كان الأذان غير مفعل، نقوم فقط بتخزين أن الصلاة قد دخل وقتها
                            localStorage.setItem(adhanPlayedKey, 'true');
                        }

                        // عرض إشعار (إذا كان متاحاً)
                        if ('Notification' in window && Notification.permission === 'granted') {
                            new Notification(`حان الآن وقت صلاة ${prayer.arabicName}`, {
                                icon: 'mosque-icon.png',
                                body: `حان وقت صلاة ${prayer.arabicName} - ${prayer.time}`
                            });
                        }

                        // تحديث قائمة الصلوات القادمة
                        updateUpcomingPrayers();

                        // تحديث العد التنازلي للصلاة القادمة
                        updateCountdown();
                        break;
                    } else {
                        console.log(`تم تشغيل الأذان لصلاة ${prayer.arabicName} اليوم مسبقاً`);
                    }
                }
            }
        }

        // تحديث قائمة الصلوات القادمة
        function updateUpcomingPrayers() {
            console.log('تحديث قائمة الصلوات القادمة');
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            const times = window.prayerTimes?.[currentCity];

            if (!times) {
                console.log('أوقات الصلوات غير متوفرة لتحديث قائمة الصلوات القادمة');
                return;
            }

            const now = new Date();
            const currentHours = now.getHours();
            const currentMinutes = now.getMinutes();
            const currentTimeInMinutes = currentHours * 60 + currentMinutes;

            // ترتيب الصلوات
            const prayers = [
                { name: 'الفجر', time: times.fajr, id: 'fajr' },
                { name: 'الشروق', time: times.sunrise, id: 'sunrise' },
                { name: 'الظهر', time: times.dhuhr, id: 'dhuhr' },
                { name: 'العصر', time: times.asr, id: 'asr' },
                { name: 'المغرب', time: times.maghrib, id: 'maghrib' },
                { name: 'العشاء', time: times.isha, id: 'isha' }
            ];

            // تحويل وقت الصلاة إلى دقائق للمقارنة
            prayers.forEach(prayer => {
                if (prayer.time) {
                    const [hours, minutes] = prayer.time.split(':').map(Number);
                    prayer.timeInMinutes = hours * 60 + minutes;
                } else {
                    prayer.timeInMinutes = -1; // قيمة غير صالحة إذا كان الوقت غير متاح
                }
            });

            // البحث عن الصلاة القادمة
            let nextPrayer = null;
            let minTimeToNextPrayer = Infinity;

            // حساب الوقت المتبقي لكل صلاة
            for (const prayer of prayers) {
                if (!prayer.time) continue;

                // تحويل وقت الصلاة إلى دقائق للمقارنة
                if (prayer.time) {
                    const [hours, minutes] = prayer.time.split(':').map(Number);
                    prayer.timeInMinutes = hours * 60 + minutes;
                } else {
                    prayer.timeInMinutes = -1; // قيمة غير صالحة إذا كان الوقت غير متاح
                    continue;
                }

                // حساب الوقت المتبقي للصلاة
                let timeToNextPrayer = prayer.timeInMinutes - currentTimeInMinutes;

                // إذا كان الوقت سالباً (أي أن الصلاة قد مرت)، أضف 24 ساعة
                if (timeToNextPrayer < 0) {
                    timeToNextPrayer += 24 * 60; // 24 ساعة بالدقائق
                }

                // إذا كان هذا أقرب صلاة قادمة
                if (timeToNextPrayer < minTimeToNextPrayer) {
                    minTimeToNextPrayer = timeToNextPrayer;
                    nextPrayer = prayer;
                }
            }

            // التأكد من وجود صلاة قادمة
            if (!nextPrayer) {
                console.error('لم يتم العثور على صلاة قادمة في updateUpcomingPrayers!');
                nextPrayer = prayers[0]; // استخدام الفجر كاحتياطي
            }

            // تحديث نص الصلاة القادمة
            const nextPrayerText = document.querySelector('.next-prayer-text');
            if (nextPrayerText) {
                nextPrayerText.textContent = `ننتظر صلاة ${nextPrayer.name}`;
            }
        }

        // دالة لبدء العد التنازلي للإقامة
        function startIqamahCountdown(prayerName, arabicName) {
            // إلغاء أي مؤقت سابق للإقامة
            if (window.iqamahTimer) {
                clearInterval(window.iqamahTimer);
            }

            console.log(`بدء العد التنازلي للإقامة لصلاة ${arabicName} (${prayerName})...`);

            // تحميل مدة الإقامة المحفوظة
            if (typeof loadIqamahTimes === 'function') {
                loadIqamahTimes();
                console.log('تم تحميل مدة الإقامة المحفوظة');
            }

            // استدعاء الدالة الجديدة من ملف iqama-countdown.js
            startFullScreenIqamaCountdown(prayerName, arabicName);

            // إنهاء الدالة هنا لاستخدام الدالة الجديدة بدلاً من الكود القديم
            return;

            // الحصول على مدة الإقامة بالدقائق
            let iqamahMinutes = 10; // القيمة الافتراضية

            try {
                // محاولة الحصول على مدة الإقامة من حقول الإدخال مباشرة
                const inputElement = document.getElementById(`${prayerName}-iqama-duration`);
                if (inputElement) {
                    iqamahMinutes = parseInt(inputElement.value) || 1;
                    console.log(`تم استخدام مدة الإقامة من حقل الإدخال: ${iqamahMinutes} دقيقة`);
                }
                // إذا لم يتم العثور على حقل الإدخال، استخدم المتغيرات العالمية
                else if (window.iqamahTimes && window.iqamahTimes[prayerName]) {
                    iqamahMinutes = window.iqamahTimes[prayerName];
                    console.log(`تم استخدام مدة الإقامة من المتغير العالمي: ${iqamahMinutes} دقيقة`);
                }
                // إذا لم يتم العثور على المتغيرات العالمية، استخدم التخزين المحلي
                else {
                    // محاولة تحميل مدة الإقامة من التخزين المحلي مباشرة
                    const savedDurations = JSON.parse(localStorage.getItem('iqama_durations')) || {};
                    if (savedDurations && savedDurations[prayerName]) {
                        iqamahMinutes = savedDurations[prayerName];
                        console.log(`تم استخدام مدة الإقامة من التخزين المحلي: ${iqamahMinutes} دقيقة`);
                    } else {
                        console.log(`لم يتم العثور على مدة إقامة محفوظة لصلاة ${prayerName}، استخدام القيمة الافتراضية: ${iqamahMinutes} دقيقة`);
                    }
                }
            } catch (error) {
                console.warn(`خطأ في تحميل مدة الإقامة: ${error.message}`);
                console.log(`استخدام القيمة الافتراضية لمدة الإقامة: ${iqamahMinutes} دقيقة`);
            }

            // تحديث المتغيرات العالمية بمدة الإقامة المستخدمة
            if (!window.iqamahTimes) {
                window.iqamahTimes = {};
            }
            window.iqamahTimes[prayerName] = iqamahMinutes;

            // تحديث المتغير العالمي الآخر للتوافق
            if (!window.iqamaTimes) {
                window.iqamaTimes = {};
            }
            window.iqamaTimes[prayerName] = iqamahMinutes;

            console.log('تم تحديث المتغيرات العالمية بمدة الإقامة المستخدمة:', iqamahMinutes);

            // تحويل إلى ثواني
            let secondsLeft = iqamahMinutes * 60;

            // إنشاء أو الحصول على عنصر التعتيم
            let darknessOverlay = document.getElementById('darkness-overlay');
            if (!darknessOverlay) {
                darknessOverlay = document.createElement('div');
                darknessOverlay.id = 'darkness-overlay';
                darknessOverlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: black;
                    z-index: 9999;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    opacity: 0;
                    transition: opacity 1s ease;
                `;
                document.body.appendChild(darknessOverlay);
            }

            // إنشاء أو الحصول على عنصر العد التنازلي
            let countdownDisplay = document.getElementById('fullscreen-countdown');
            if (!countdownDisplay) {
                countdownDisplay = document.createElement('div');
                countdownDisplay.id = 'fullscreen-countdown';
                countdownDisplay.style.cssText = `
                    color: white;
                    font-size: 10vw;
                    font-weight: bold;
                    text-align: center;
                    direction: rtl;
                    font-family: 'Amiri', 'Traditional Arabic', Arial, sans-serif;
                `;
                darknessOverlay.appendChild(countdownDisplay);
            }

            // تفعيل التعتيم وعرض العد التنازلي
            darknessOverlay.style.display = 'flex';
            darknessOverlay.style.opacity = '1';
            countdownDisplay.innerHTML = `
                <div style="margin-bottom: 20px; font-size: 5vw;">صلاة ${arabicName}</div>
                <div style="font-size: 15vw; margin-bottom: 20px;" id="iqamah-countdown-time">
                    ${String(Math.floor(secondsLeft / 60)).padStart(2, '0')}:${String(secondsLeft % 60).padStart(2, '0')}
                </div>
                <div style="font-size: 4vw;">الوقت المتبقي للإقامة</div>
            `;

            console.log('تم تفعيل شاشة التعتيم وعرض العد التنازلي للإقامة');

            // تحديث العد التنازلي كل ثانية
            window.iqamahTimer = setInterval(() => {
                secondsLeft--;

                const minutes = Math.floor(secondsLeft / 60);
                const seconds = secondsLeft % 60;

                // تحديث العد التنازلي بخط كبير وواضح
                const countdownTimeElement = document.getElementById('iqamah-countdown-time');
                if (countdownTimeElement) {
                    countdownTimeElement.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
                }

                if (secondsLeft <= 0) {
                    clearInterval(window.iqamahTimer);
                    window.iqamahTimer = null;

                    // إيقاف النص المتغير
                    if (window.textChangeInterval) {
                        clearInterval(window.textChangeInterval);
                        window.textChangeInterval = null;
                        console.log('تم إيقاف النص المتغير');
                    }

                    // تحديث النص عند انتهاء العد التنازلي
                    countdownDisplay.innerHTML = `
                        <div style="margin-bottom: 20px; font-size: 5vw;">صلاة ${arabicName}</div>
                        <div style="font-size: 15vw; margin-bottom: 20px;">00:00</div>
                        <div style="font-size: 4vw;">حان وقت الإقامة</div>
                    `;

                    // تشغيل صوت الإقامة
                    const iqamaAudio = document.getElementById('iqama-audio');
                    if (iqamaAudio) {
                        iqamaAudio.play()
                            .then(() => {
                                console.log('بدأ تشغيل صوت الإقامة');

                                // الحصول على مدة التعتيم المحفوظة
                                let darknessMinutes = 10; // القيمة الافتراضية

                                // محاولة الحصول من localStorage أولاً
                                try {
                                    const savedDarkness = localStorage.getItem('darknessTimes');
                                    if (savedDarkness) {
                                        const darknessTimes = JSON.parse(savedDarkness);
                                        if (darknessTimes[prayerName]) {
                                            darknessMinutes = parseInt(darknessTimes[prayerName]);
                                            console.log(`✅ تم الحصول على مدة التعتيم من localStorage: ${darknessMinutes} دقيقة`);
                                        }
                                    }
                                } catch (error) {
                                    console.error('خطأ في قراءة مدة التعتيم من localStorage:', error);
                                }

                                // إذا لم نجد في localStorage، نحاول من المتغير العالمي
                                if (darknessMinutes === 10 && window.darknessTimes?.[prayerName]) {
                                    darknessMinutes = parseInt(window.darknessTimes[prayerName]);
                                    console.log(`✅ تم الحصول على مدة التعتيم من المتغير العالمي: ${darknessMinutes} دقيقة`);
                                }

                                // إذا لم نجد، نحاول من حقل الإدخال مباشرة
                                if (darknessMinutes === 10) {
                                    const darknessInput = document.getElementById(`${prayerName}-darkness`);
                                    if (darknessInput && darknessInput.value) {
                                        darknessMinutes = parseInt(darknessInput.value);
                                        console.log(`✅ تم الحصول على مدة التعتيم من حقل الإدخال: ${darknessMinutes} دقيقة`);
                                    }
                                }

                                console.log(`🌙 مدة التعتيم النهائية لصلاة ${prayerName}: ${darknessMinutes} دقيقة`);

                                // إزالة محتوى شاشة التعتيم
                                while (darknessOverlay.firstChild) {
                                    darknessOverlay.removeChild(darknessOverlay.firstChild);
                                }

                                // إنشاء عنصر الساعة الرقمية
                                const digitalClock = document.createElement('div');
                                digitalClock.id = 'fullscreen-digital-clock';
                                digitalClock.style.cssText = `
                                    color: white;
                                    font-size: 20vw;
                                    font-weight: bold;
                                    text-align: center;
                                    direction: ltr;
                                    font-family: 'Digital-7', 'Courier New', monospace;
                                    text-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
                                `;
                                darknessOverlay.appendChild(digitalClock);

                                // تحديث الساعة الرقمية كل ثانية
                                const updateDigitalClock = () => {
                                    const now = new Date();
                                    const hours = String(now.getHours()).padStart(2, '0');
                                    const minutes = String(now.getMinutes()).padStart(2, '0');
                                    const seconds = String(now.getSeconds()).padStart(2, '0');
                                    digitalClock.textContent = `${hours}:${minutes}:${seconds}`;
                                };

                                // تحديث الساعة مباشرة ثم كل ثانية
                                updateDigitalClock();

                                // إلغاء أي مؤقت سابق للساعة الرقمية
                                if (window.digitalClockInterval) {
                                    clearInterval(window.digitalClockInterval);
                                }

                                // بدء تحديث الساعة كل ثانية
                                window.digitalClockInterval = setInterval(updateDigitalClock, 1000);

                                // إخفاء التعتيم بعد المدة المحددة
                                setTimeout(() => {
                                    // إيقاف تحديث الساعة الرقمية
                                    if (window.digitalClockInterval) {
                                        clearInterval(window.digitalClockInterval);
                                        window.digitalClockInterval = null;
                                    }

                                    darknessOverlay.style.opacity = '0';
                                    setTimeout(() => {
                                        darknessOverlay.style.display = 'none';

                                        // إعادة تشغيل النص المتغير بعد انتهاء التعتيم
                                        if (typeof startTextChange === 'function') {
                                            startTextChange();
                                            console.log('تم إعادة تشغيل النص المتغير');
                                        }
                                    }, 1000);
                                }, darknessMinutes * 60 * 1000);
                                console.log(`🌙 سيتم إنهاء التعتيم بعد ${darknessMinutes} دقيقة`);
                            })
                            .catch(err => {
                                console.error('خطأ في تشغيل صوت الإقامة:', err);

                                // إزالة محتوى شاشة التعتيم
                                while (darknessOverlay.firstChild) {
                                    darknessOverlay.removeChild(darknessOverlay.firstChild);
                                }

                                // إنشاء عنصر الساعة الرقمية
                                const digitalClock = document.createElement('div');
                                digitalClock.id = 'fullscreen-digital-clock';
                                digitalClock.style.cssText = `
                                    color: white;
                                    font-size: 20vw;
                                    font-weight: bold;
                                    text-align: center;
                                    direction: ltr;
                                    font-family: 'Digital-7', 'Courier New', monospace;
                                    text-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
                                `;
                                darknessOverlay.appendChild(digitalClock);

                                // تحديث الساعة الرقمية كل ثانية
                                const updateDigitalClock = () => {
                                    const now = new Date();
                                    const hours = String(now.getHours()).padStart(2, '0');
                                    const minutes = String(now.getMinutes()).padStart(2, '0');
                                    const seconds = String(now.getSeconds()).padStart(2, '0');
                                    digitalClock.textContent = `${hours}:${minutes}:${seconds}`;
                                };

                                // تحديث الساعة مباشرة ثم كل ثانية
                                updateDigitalClock();

                                // إلغاء أي مؤقت سابق للساعة الرقمية
                                if (window.digitalClockInterval) {
                                    clearInterval(window.digitalClockInterval);
                                }

                                // بدء تحديث الساعة كل ثانية
                                window.digitalClockInterval = setInterval(updateDigitalClock, 1000);

                                // إخفاء التعتيم بعد المدة المحددة في حالة الخطأ
                                console.log(`🌙 سيتم إنهاء التعتيم بعد ${darknessMinutes} دقيقة (حالة خطأ)`);
                                setTimeout(() => {
                                    // إيقاف تحديث الساعة الرقمية
                                    if (window.digitalClockInterval) {
                                        clearInterval(window.digitalClockInterval);
                                        window.digitalClockInterval = null;
                                    }

                                    darknessOverlay.style.opacity = '0';
                                    setTimeout(() => {
                                        darknessOverlay.style.display = 'none';

                                        // إعادة تشغيل النص المتغير بعد انتهاء التعتيم
                                        if (typeof startTextChange === 'function') {
                                            startTextChange();
                                            console.log('تم إعادة تشغيل النص المتغير');
                                        }
                                    }, 1000);
                                }, darknessMinutes * 60 * 1000);
                            });
                    } else {
                        console.error('عنصر صوت الإقامة غير موجود');

                        // إزالة محتوى شاشة التعتيم
                        while (darknessOverlay.firstChild) {
                            darknessOverlay.removeChild(darknessOverlay.firstChild);
                        }

                        // إنشاء عنصر الساعة الرقمية
                        const digitalClock = document.createElement('div');
                        digitalClock.id = 'fullscreen-digital-clock';
                        digitalClock.style.cssText = `
                            color: white;
                            font-size: 20vw;
                            font-weight: bold;
                            text-align: center;
                            direction: ltr;
                            font-family: 'Digital-7', 'Courier New', monospace;
                            text-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
                        `;
                        darknessOverlay.appendChild(digitalClock);

                        // تحديث الساعة الرقمية كل ثانية
                        const updateDigitalClock = () => {
                            const now = new Date();
                            const hours = String(now.getHours()).padStart(2, '0');
                            const minutes = String(now.getMinutes()).padStart(2, '0');
                            const seconds = String(now.getSeconds()).padStart(2, '0');
                            digitalClock.textContent = `${hours}:${minutes}:${seconds}`;
                        };

                        // تحديث الساعة مباشرة ثم كل ثانية
                        updateDigitalClock();

                        // إلغاء أي مؤقت سابق للساعة الرقمية
                        if (window.digitalClockInterval) {
                            clearInterval(window.digitalClockInterval);
                        }

                        // بدء تحديث الساعة كل ثانية
                        window.digitalClockInterval = setInterval(updateDigitalClock, 1000);

                        // إخفاء التعتيم بعد المدة المحددة في حالة عدم وجود صوت الإقامة
                        console.log(`🌙 سيتم إنهاء التعتيم بعد ${darknessMinutes} دقيقة (عدم وجود صوت)`);
                        setTimeout(() => {
                            // إيقاف تحديث الساعة الرقمية
                            if (window.digitalClockInterval) {
                                clearInterval(window.digitalClockInterval);
                                window.digitalClockInterval = null;
                            }

                            darknessOverlay.style.opacity = '0';
                            setTimeout(() => {
                                darknessOverlay.style.display = 'none';

                                // إعادة تشغيل النص المتغير بعد انتهاء التعتيم
                                if (typeof startTextChange === 'function') {
                                    startTextChange();
                                    console.log('تم إعادة تشغيل النص المتغير');
                                }
                            }, 1000);
                        }, darknessMinutes * 60 * 1000);
                    }
                }
            }, 1000);

            // إضافة زر إغلاق للتعتيم
            const closeButton = document.createElement('button');
            closeButton.style.cssText = `
                position: absolute;
                top: 20px;
                right: 20px;
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                font-size: 24px;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                justify-content: center;
                align-items: center;
            `;
            closeButton.innerHTML = '×';
            closeButton.onclick = function() {
                clearInterval(window.iqamahTimer);
                darknessOverlay.style.opacity = '0';
                setTimeout(() => {
                    darknessOverlay.style.display = 'none';
                }, 1000);
            };
            darknessOverlay.appendChild(closeButton);
        }

        // دالة لتحميل مدة الإقامة المحفوظة
        function loadIqamahTimes() {
            try {
                console.log('بدء تحميل مدة الإقامة المحفوظة...');

                // محاولة تحميل مدة الإقامة من التخزين المحلي
                let savedTimes = {};

                try {
                    // محاولة تحميل من التخزين المحلي
                    const savedData = localStorage.getItem('iqama_durations');
                    if (savedData) {
                        savedTimes = JSON.parse(savedData);
                        console.log('تم تحميل مدة الإقامة من التخزين المحلي:', savedTimes);
                    }
                } catch (error) {
                    console.warn('خطأ في تحميل مدة الإقامة من التخزين المحلي:', error);
                }

                // إذا لم يتم العثور على بيانات، استخدم القيم الافتراضية
                if (Object.keys(savedTimes).length === 0) {
                    savedTimes = {
                        fajr: 10,
                        dhuhr: 10,
                        asr: 10,
                        maghrib: 10,
                        isha: 10
                    };
                    console.log('استخدام القيم الافتراضية لمدة الإقامة:', savedTimes);
                }

                // تحديث قيم حقول الإدخال
                if (document.getElementById('fajr-iqama-duration')) {
                    document.getElementById('fajr-iqama-duration').value = savedTimes.fajr || 10;
                }
                if (document.getElementById('dhuhr-iqama-duration')) {
                    document.getElementById('dhuhr-iqama-duration').value = savedTimes.dhuhr || 10;
                }
                if (document.getElementById('asr-iqama-duration')) {
                    document.getElementById('asr-iqama-duration').value = savedTimes.asr || 10;
                }
                if (document.getElementById('maghrib-iqama-duration')) {
                    document.getElementById('maghrib-iqama-duration').value = savedTimes.maghrib || 10;
                }
                if (document.getElementById('isha-iqama-duration')) {
                    document.getElementById('isha-iqama-duration').value = savedTimes.isha || 10;
                }

                // تعريف متغير iqamahTimes العالمي
                window.iqamahTimes = {
                    fajr: parseInt(document.getElementById('fajr-iqama-duration').value) || 1,
                    dhuhr: parseInt(document.getElementById('dhuhr-iqama-duration').value) || 1,
                    asr: parseInt(document.getElementById('asr-iqama-duration').value) || 1,
                    maghrib: parseInt(document.getElementById('maghrib-iqama-duration').value) || 1,
                    isha: parseInt(document.getElementById('isha-iqama-duration').value) || 1
                };

                // تعريف متغير iqamaTimes العالمي للتوافق
                window.iqamaTimes = window.iqamahTimes;

                console.log('تم تحميل وتعريف متغيرات مدة الإقامة العالمية:', window.iqamahTimes);

                return true;
            } catch (error) {
                console.error('خطأ في تحميل مدة الإقامة المحفوظة:', error);

                // استخدام القيم الافتراضية في حالة الخطأ
                window.iqamahTimes = {
                    fajr: 10,
                    dhuhr: 10,
                    asr: 10,
                    maghrib: 10,
                    isha: 10
                };

                window.iqamaTimes = window.iqamahTimes;

                return false;
            }
        }

        // دالة لحفظ مدة الإقامة
        function saveIqamahTimes() {
            try {
                console.log('بدء حفظ مدة الإقامة...');

                // قراءة القيم من حقول الإدخال
                const iqamahTimes = {
                    fajr: parseInt(document.getElementById('fajr-iqama-duration').value) || 1,
                    dhuhr: parseInt(document.getElementById('dhuhr-iqama-duration').value) || 1,
                    asr: parseInt(document.getElementById('asr-iqama-duration').value) || 1,
                    maghrib: parseInt(document.getElementById('maghrib-iqama-duration').value) || 1,
                    isha: parseInt(document.getElementById('isha-iqama-duration').value) || 1
                };

                // التحقق من صحة القيم
                let isValid = true;
                let invalidValues = [];

                Object.entries(iqamahTimes).forEach(([prayer, duration]) => {
                    if (isNaN(duration) || duration < 1 || duration > 40) {
                        isValid = false;
                        invalidValues.push(`${prayer}: ${duration}`);
                    }
                });

                if (!isValid) {
                    console.error('قيم غير صالحة لمدة الإقامة:', invalidValues);

                    // عرض إشعار خطأ
                    if (typeof showNotification === 'function') {
                        showNotification('قيم غير صالحة لمدة الإقامة. يجب أن تكون القيم بين 1 و 40 دقيقة.', 'error');
                    } else {
                        alert('قيم غير صالحة لمدة الإقامة. يجب أن تكون القيم بين 1 و 40 دقيقة.');
                    }

                    return false;
                }

                // حفظ القيم في التخزين المحلي بمفتاح واحد فقط للتبسيط
                localStorage.setItem('iqama_durations', JSON.stringify(iqamahTimes));

                // تحديث المتغيرات العالمية
                window.iqamahTimes = iqamahTimes;
                window.iqamaTimes = iqamahTimes;

                // تحديث المتغير العالمي الرئيسي
                window.iqamaTimes = iqamahTimes;

                console.log('تم حفظ مدة الإقامة بنجاح:', iqamahTimes);

                // عرض إشعار نجاح
                alert('تم حفظ مدة الإقامة بنجاح: ' + JSON.stringify(iqamahTimes));

                // تحديث العرض إذا كان هناك عد تنازلي نشط
                if (window.iqamahTimer) {
                    console.log('يوجد عد تنازلي نشط، سيتم تحديثه بالقيم الجديدة');

                    // الحصول على الصلاة الحالية
                    const currentPrayer = document.getElementById('iqamah-prayer-name')?.textContent?.replace('صلاة ', '') || '';
                    if (currentPrayer) {
                        const prayerKey = getPrayerKey(currentPrayer);
                        if (prayerKey && iqamahTimes[prayerKey]) {
                            console.log(`إعادة تشغيل العد التنازلي للإقامة لصلاة ${currentPrayer} (${prayerKey}) بالقيمة الجديدة: ${iqamahTimes[prayerKey]} دقيقة`);
                            startIqamahCountdown(prayerKey, currentPrayer);
                        }
                    }
                }

                return true;
            } catch (error) {
                console.error('خطأ في حفظ مدة الإقامة:', error);

                // عرض إشعار خطأ
                alert('حدث خطأ أثناء حفظ مدة الإقامة: ' + error.message);

                return false;
            }
        }

        // دالة مساعدة للحصول على مفتاح الصلاة من الاسم العربي
        function getPrayerKey(arabicName) {
            const prayerMap = {
                'الفجر': 'fajr',
                'الشروق': 'sunrise',
                'الظهر': 'dhuhr',
                'العصر': 'asr',
                'المغرب': 'maghrib',
                'العشاء': 'isha'
            };

            return prayerMap[arabicName] || '';
        }

        // دالة لعرض الساعة الرقمية على كامل الشاشة
        function showDigitalClockOverlay(darknessOverlay, durationMinutes) {
            console.log('عرض الساعة الرقمية على كامل الشاشة لمدة', durationMinutes, 'دقيقة');

            // إزالة أي محتوى سابق من شاشة التعتيم
            while (darknessOverlay.firstChild) {
                darknessOverlay.removeChild(darknessOverlay.firstChild);
            }

            // إنشاء عنصر الساعة الرقمية
            const digitalClock = document.createElement('div');
            digitalClock.id = 'fullscreen-digital-clock';
            digitalClock.style.cssText = `
                color: white;
                font-size: 20vw;
                font-weight: bold;
                text-align: center;
                direction: ltr;
                font-family: 'Digital-7', 'Courier New', monospace;
                text-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
            `;
            darknessOverlay.appendChild(digitalClock);

            // تحديث الساعة الرقمية كل ثانية
            const updateDigitalClock = () => {
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                digitalClock.textContent = `${hours}:${minutes}:${seconds}`;
            };

            // تحديث الساعة مباشرة ثم كل ثانية
            updateDigitalClock();

            // إلغاء أي مؤقت سابق للساعة الرقمية
            if (window.digitalClockInterval) {
                clearInterval(window.digitalClockInterval);
            }

            // بدء تحديث الساعة كل ثانية
            window.digitalClockInterval = setInterval(updateDigitalClock, 1000);

            // إضافة زر إغلاق للساعة الرقمية
            const closeButton = document.createElement('button');
            closeButton.style.cssText = `
                position: absolute;
                top: 20px;
                right: 20px;
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                font-size: 24px;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                justify-content: center;
                align-items: center;
            `;
            closeButton.innerHTML = '×';
            closeButton.onclick = function() {
                if (window.digitalClockInterval) {
                    clearInterval(window.digitalClockInterval);
                    window.digitalClockInterval = null;
                }
                darknessOverlay.style.opacity = '0';
                setTimeout(() => {
                    darknessOverlay.style.display = 'none';

                    // إعادة تشغيل النص المتغير بعد إغلاق الساعة الرقمية
                    if (typeof startTextChange === 'function') {
                        startTextChange();
                        console.log('تم إعادة تشغيل النص المتغير');
                    }
                }, 1000);
            };
            darknessOverlay.appendChild(closeButton);

            // إيقاف تحديث الساعة الرقمية بعد انتهاء المدة المحددة
            setTimeout(() => {
                if (window.digitalClockInterval) {
                    clearInterval(window.digitalClockInterval);
                    window.digitalClockInterval = null;
                }
            }, durationMinutes * 60 * 1000);

            return digitalClock;
        }

        // دالة لحفظ إعدادات التعتيم
        function saveDarknessTimes() {
            try {
                console.log('🌙 بدء حفظ إعدادات التعتيم...');

                const darknessTimes = {
                    fajr: parseInt(document.getElementById('fajr-darkness')?.value || document.getElementById('fajr-darkness-duration')?.value) || 10,
                    dhuhr: parseInt(document.getElementById('dhuhr-darkness')?.value || document.getElementById('dhuhr-darkness-duration')?.value) || 10,
                    asr: parseInt(document.getElementById('asr-darkness')?.value || document.getElementById('asr-darkness-duration')?.value) || 10,
                    maghrib: parseInt(document.getElementById('maghrib-darkness')?.value || document.getElementById('maghrib-darkness-duration')?.value) || 10,
                    isha: parseInt(document.getElementById('isha-darkness')?.value || document.getElementById('isha-darkness-duration')?.value) || 10
                };

                console.log('🌙 قيم التعتيم المقروءة:', darknessTimes);

                localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
                console.log('تم حفظ إعدادات التعتيم بنجاح:', darknessTimes);

                // تحديث المتغير العالمي
                window.darknessTimes = darknessTimes;

                // حفظ حالة تفعيل التعتيم
                const enableDarkness = document.getElementById('enable-darkness')?.checked !== false;
                const darknessSettings = { enabled: enableDarkness };
                localStorage.setItem('darknessSettings', JSON.stringify(darknessSettings));
                console.log('تم حفظ حالة تفعيل التعتيم:', enableDarkness ? 'مفعل' : 'غير مفعل');

                // تحديث المتغير العالمي
                window.enableDarkness = enableDarkness;

                showNotification('تم حفظ إعدادات التعتيم بنجاح', 'success');
                return true;
            } catch (error) {
                console.error('خطأ في حفظ إعدادات التعتيم:', error);
                showNotification('حدث خطأ أثناء حفظ إعدادات التعتيم', 'error');
                return false;
            }
        }

        // دالة لتفعيل أو تعطيل التعتيم
        function toggleDarkness(enable) {
            try {
                const darknessSettings = { enabled: enable };
                localStorage.setItem('darknessSettings', JSON.stringify(darknessSettings));

                // تحديث المتغير العالمي
                window.enableDarkness = enable;

                console.log('تم تغيير حالة التعتيم إلى:', enable ? 'مفعل' : 'غير مفعل');

                // تحديث حالة عنصر التحكم
                const enableDarknessCheckbox = document.getElementById('enable-darkness');
                if (enableDarknessCheckbox) {
                    enableDarknessCheckbox.checked = enable;
                }

                showNotification(`تم ${enable ? 'تفعيل' : 'تعطيل'} التعتيم بنجاح`, 'success');
                return true;
            } catch (error) {
                console.error('خطأ في تغيير حالة التعتيم:', error);
                showNotification('حدث خطأ أثناء تغيير حالة التعتيم', 'error');
                return false;
            }
        }

        // دالة لتحميل إعدادات التعتيم
        function loadDarknessTimes() {
            try {
                // تحميل مدة التعتيم
                const savedDarknessTimes = localStorage.getItem('darknessTimes');
                if (savedDarknessTimes) {
                    const darknessTimes = JSON.parse(savedDarknessTimes);

                    // تحديث المتغير العالمي
                    window.darknessTimes = darknessTimes;

                    // تحديث حقول الإدخال
                    if (document.getElementById('fajr-darkness-duration')) {
                        document.getElementById('fajr-darkness-duration').value = darknessTimes.fajr || 10;
                    }
                    if (document.getElementById('dhuhr-darkness-duration')) {
                        document.getElementById('dhuhr-darkness-duration').value = darknessTimes.dhuhr || 10;
                    }
                    if (document.getElementById('asr-darkness-duration')) {
                        document.getElementById('asr-darkness-duration').value = darknessTimes.asr || 10;
                    }
                    if (document.getElementById('maghrib-darkness-duration')) {
                        document.getElementById('maghrib-darkness-duration').value = darknessTimes.maghrib || 10;
                    }
                    if (document.getElementById('isha-darkness-duration')) {
                        document.getElementById('isha-darkness-duration').value = darknessTimes.isha || 10;
                    }

                    console.log('تم تحميل مدة التعتيم بنجاح:', darknessTimes);
                } else {
                    // إعدادات افتراضية لمدة التعتيم
                    window.darknessTimes = {
                        fajr: 10,
                        dhuhr: 10,
                        asr: 10,
                        maghrib: 10,
                        isha: 10
                    };
                    console.log('تم استخدام مدة التعتيم الافتراضية');
                }

                // تحميل حالة تفعيل التعتيم
                const savedDarknessSettings = localStorage.getItem('darknessSettings');
                if (savedDarknessSettings) {
                    const darknessSettings = JSON.parse(savedDarknessSettings);

                    // تحديث المتغير العالمي
                    window.enableDarkness = darknessSettings.enabled;

                    // تحديث حالة عنصر التحكم
                    const enableDarknessCheckbox = document.getElementById('enable-darkness');
                    if (enableDarknessCheckbox) {
                        enableDarknessCheckbox.checked = darknessSettings.enabled;
                    }

                    console.log('تم تحميل حالة تفعيل التعتيم بنجاح:', darknessSettings.enabled ? 'مفعل' : 'غير مفعل');
                } else {
                    // حالة افتراضية لتفعيل التعتيم
                    window.enableDarkness = true;

                    // تحديث حالة عنصر التحكم
                    const enableDarknessCheckbox = document.getElementById('enable-darkness');
                    if (enableDarknessCheckbox) {
                        enableDarknessCheckbox.checked = true;
                    }

                    console.log('تم استخدام حالة تفعيل التعتيم الافتراضية: مفعل');
                }

                return true;
            } catch (error) {
                console.error('خطأ في تحميل إعدادات التعتيم:', error);

                // إعدادات افتراضية
                window.darknessTimes = {
                    fajr: 10,
                    dhuhr: 10,
                    asr: 10,
                    maghrib: 10,
                    isha: 10
                };

                window.enableDarkness = true;

                return false;
            }
        }

        // إضافة مستمعي الأحداث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل مدة الإقامة المحفوظة
            loadIqamahTimes();

            // تحميل إعدادات التعتيم المحفوظة
            loadDarknessTimes();

            // إضافة مستمع لحفظ مدة الإقامة
            const saveIqamahTimesBtn = document.getElementById('save-iqamah-times');
            if (saveIqamahTimesBtn) {
                saveIqamahTimesBtn.addEventListener('click', saveIqamahTimes);
            }

            // إضافة مستمعي أحداث لحقول مدة الإقامة
            const iqamaDurationInputs = [
                document.getElementById('fajr-iqama-duration'),
                document.getElementById('dhuhr-iqama-duration'),
                document.getElementById('asr-iqama-duration'),
                document.getElementById('maghrib-iqama-duration'),
                document.getElementById('isha-iqama-duration')
            ];

            // إضافة مستمع لكل حقل إدخال
            iqamaDurationInputs.forEach(input => {
                if (input) {
                    // إضافة مستمع لتغيير القيمة
                    input.addEventListener('change', function() {
                        console.log(`تم تغيير قيمة مدة الإقامة لـ ${input.id}: ${input.value}`);

                        // حفظ القيم الجديدة
                        saveIqamahTimes();
                    });

                    // إضافة مستمع للتحقق من صحة القيمة
                    input.addEventListener('input', function() {
                        const value = parseInt(input.value);
                        if (isNaN(value) || value < 1) {
                            input.value = 1;
                        } else if (value > 40) {
                            input.value = 40;
                        }
                    });
                }
            });

            // إضافة مستمع لاختبار مدة الإقامة
            const testIqamahTimesBtn = document.getElementById('test-iqamah-times');
            if (testIqamahTimesBtn) {
                testIqamahTimesBtn.addEventListener('click', function() {
                    try {
                        console.log('اختبار مدة الإقامة...');

                        // تحديث المتغيرات العالمية من حقول الإدخال
                        window.iqamahTimes = {
                            fajr: parseInt(document.getElementById('fajr-iqama-duration').value) || 1,
                            dhuhr: parseInt(document.getElementById('dhuhr-iqama-duration').value) || 1,
                            asr: parseInt(document.getElementById('asr-iqama-duration').value) || 1,
                            maghrib: parseInt(document.getElementById('maghrib-iqama-duration').value) || 1,
                            isha: parseInt(document.getElementById('isha-iqama-duration').value) || 1
                        };

                        window.iqamaTimes = window.iqamahTimes;

                        console.log('تم تحديث المتغيرات العالمية:', window.iqamahTimes);

                        // بدء العد التنازلي للإقامة
                        startIqamahCountdown('asr', 'العصر');

                        // عرض إشعار
                        if (typeof showNotification === 'function') {
                            showNotification('تم بدء اختبار مدة الإقامة للعصر', 'info');
                        } else {
                            alert('تم بدء اختبار مدة الإقامة للعصر');
                        }
                    } catch (error) {
                        console.error('خطأ في اختبار مدة الإقامة:', error);

                        // عرض إشعار خطأ
                        if (typeof showNotification === 'function') {
                            showNotification('حدث خطأ أثناء اختبار مدة الإقامة', 'error');
                        } else {
                            alert('حدث خطأ أثناء اختبار مدة الإقامة');
                        }
                    }
                });
            }

            // إضافة مستمع لاختبار العد التنازلي للإقامة
            const testIqamahCountdownBtn = document.getElementById('test-iqamah-countdown');
            if (testIqamahCountdownBtn) {
                testIqamahCountdownBtn.addEventListener('click', function() {
                    try {
                        // حفظ القيم الحالية أولاً
                        saveIqamahTimes();

                        // استدعاء دالة اختبار العد التنازلي للإقامة
                        testIqamaCountdown();

                        // عرض إشعار
                        if (typeof showNotification === 'function') {
                            showNotification('تم بدء اختبار العد التنازلي للإقامة', 'info');
                        } else {
                            alert('تم بدء اختبار العد التنازلي للإقامة');
                        }
                    } catch (error) {
                        console.error('خطأ في اختبار العد التنازلي للإقامة:', error);

                        // عرض إشعار خطأ
                        if (typeof showNotification === 'function') {
                            showNotification('حدث خطأ أثناء اختبار العد التنازلي للإقامة', 'error');
                        } else {
                            alert('حدث خطأ أثناء اختبار العد التنازلي للإقامة');
                        }
                    }
                });
            }

            // إضافة مستمع لحفظ إعدادات التعتيم
            const saveDarknessTimesBtn = document.getElementById('save-darkness-times');
            if (saveDarknessTimesBtn) {
                saveDarknessTimesBtn.addEventListener('click', saveDarknessTimes);
            }
        });

        // دالة لفحص تغير التاريخ
        function checkDateChange() {
            const today = new Date().toISOString().split('T')[0];

            // التحقق من تغير التاريخ
            if (today !== currentDate) {
                console.log('تغير التاريخ، جاري تحديث أوقات الصلاة...');
                currentDate = today;

                // تحديث التاريخ المعروض
                updateDate();

                // تحديث أوقات الصلاة
                getPrayerTimes();
            }
        }

        // بدء فحص تغير التاريخ كل دقيقة
        setInterval(checkDateChange, 60000);

        // فحص دخول وقت الصلاة كل ثانية (تأكد من عدم وجود مؤقت سابق)
        if (window.prayerCheckInterval) {
            clearInterval(window.prayerCheckInterval);
        }
        window.prayerCheckInterval = setInterval(checkPrayerTimeAndPlayAdhan, 1000);

        // إنشاء المستطيل الأفقي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء المستطيل الأفقي
            createPrayerTimesBar();

            // تحديث المواقيت في المستطيل الأفقي
            updatePrayerTimes();

            // تحديث المواقيت في المستطيل الأفقي كل دقيقة
            setInterval(updatePrayerTimes, 60000);
        });

        // دالة لتحديث مواقيت الصلاة في المستطيل الأفقي
        function updatePrayerTimes() {
            console.log('تحديث مواقيت الصلاة في المستطيل الأفقي...');

            // التأكد من وجود المستطيل الأفقي
            createPrayerTimesBar();

            // الحصول على المدينة الحالية
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            console.log('المدينة الحالية:', currentCity);

            // محاولة الحصول على المواقيت من window.prayerTimes أولاً
            let times = window.prayerTimes?.[currentCity];

            // إذا لم تكن متوفرة، استخدم المواقيت الثابتة
            if (!times) {
                console.warn('مواقيت الصلاة غير متوفرة للعرض في المستطيل الأفقي، استخدام المواقيت الثابتة...');
                times = window.AMMAN_PRAYER_TIMES[currentCity];

                // إذا لم تكن المواقيت متوفرة في المواقيت الثابتة
                if (!times) {
                    console.warn('مواقيت الصلاة غير متوفرة للمدينة المحددة، استخدام مواقيت عمان كاحتياطي');
                    times = window.AMMAN_PRAYER_TIMES['Asia/Amman'];

                    // إذا لم تكن مواقيت عمان متوفرة
                    if (!times) {
                        console.error('لا توجد مواقيت احتياطية متاحة، استخدام مواقيت افتراضية');

                        // إنشاء مواقيت افتراضية
                        times = {
                            fajr: '04:00',
                            sunrise: '05:30',
                            dhuhr: '12:00',
                            asr: '15:30',
                            maghrib: '18:00',
                            isha: '19:30'
                        };

                        console.log('تم استخدام مواقيت افتراضية');
                    } else {
                        console.log('تم استخدام مواقيت عمان كاحتياطي');
                    }
                } else {
                    console.log('تم استخدام المواقيت الثابتة للمدينة المحددة');
                }
            } else {
                console.log('تم استخدام المواقيت المحفوظة للمدينة المحددة');
            }

            // تحديث المواقيت العالمية
            if (!window.prayerTimes) {
                window.prayerTimes = {};
            }

            // تحديث المواقيت العالمية للمدينة الحالية
            window.prayerTimes[currentCity] = times;

            console.log('تم تحديث مواقيت الصلاة للمدينة:', currentCity, times);

            // تحديث عرض المواقيت
            updatePrayerTimesDisplay(times, '24');

            // تحديث العد التنازلي للصلاة القادمة
            updateCountdown();

            // تحديث نص الصلاة القادمة
            updateNextPrayerText();

            console.log('تم تحديث مواقيت الصلاة في المستطيل الأفقي بنجاح');

            return times;
        }

        // تم حذف الدالة المكررة

        // دالة لتحديث مواقيت الصلاة في جميع أنحاء التطبيق
        function updateAllPrayerTimesInApp() {
            console.log('بدء تحديث جميع عروض مواقيت الصلاة في التطبيق...');

            try {
                // الحصول على المدينة المحددة
                const selectedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                console.log('المدينة المحددة:', selectedCity);

                // التحقق من وجود مواقيت معدلة يدوياً للمدينة المحددة
                if (manualPrayerTimesEnabled && manualPrayerTimes && manualPrayerTimes[selectedCity]) {
                    console.log('استخدام المواقيت المعدلة يدوياً للمدينة المحددة:', selectedCity);

                    // نسخ المواقيت المعدلة يدوياً إلى متغير مؤقت
                    const manualTimes = JSON.parse(JSON.stringify(manualPrayerTimes[selectedCity]));

                    // تحديث المواقيت العالمية
                    if (!window.prayerTimes) {
                        window.prayerTimes = {};
                    }

                    // تحديث المواقيت العالمية للمدينة المحددة
                    window.prayerTimes[selectedCity] = manualTimes;

                    // تحديث عرض المواقيت
                    updatePrayerTimesDisplay(manualTimes, '24');

                    // تحديث قائمة الصلوات القادمة
                    if (typeof updateUpcomingPrayers === 'function') {
                        updateUpcomingPrayers();
                    }

                    // تحديث العد التنازلي
                    if (typeof updateCountdown === 'function') {
                        updateCountdown();
                    }

                    // تحديث نص الصلاة القادمة
                    if (typeof updateNextPrayerText === 'function') {
                        updateNextPrayerText();
                    }

                    // تحديث أي عناصر أخرى تعتمد على مواقيت الصلاة
                    if (typeof displayRemainingPrayerTimes === 'function') {
                        displayRemainingPrayerTimes();
                    }

                    console.log('تم تحديث جميع عروض مواقيت الصلاة باستخدام المواقيت المعدلة يدوياً بنجاح');
                    return;
                }

                // الحصول على المواقيت
                let times = window.prayerTimes?.[selectedCity];

                // إذا لم تكن المواقيت متوفرة، حاول حسابها
                if (!times && window.prayTimes) {
                    console.log('المواقيت غير متوفرة، محاولة حسابها...');

                    try {
                        // الحصول على طريقة الحساب المحفوظة
                        const savedMethod = localStorage.getItem('calculationMethod') || 'MWL';
                        const savedJuristicMethod = localStorage.getItem('juristicMethod') || 'Shafi';

                        // تعيين طريقة الحساب
                        window.prayTimes.setMethod(savedMethod);

                        // تعيين المذهب (للعصر)
                        const juristicMethodValue = savedJuristicMethod === 'Hanafi' ? 1 : 0;
                        window.prayTimes.adjust({asr: juristicMethodValue});

                        // الحصول على إحداثيات المدينة
                        const coordinates = getCityCoordinates(selectedCity);
                        if (!coordinates || !coordinates.lat || !coordinates.lng) {
                            console.error('إحداثيات غير صالحة للمدينة:', selectedCity);
                            throw new Error('إحداثيات غير صالحة للمدينة: ' + selectedCity);
                        }

                        // الحصول على المنطقة الزمنية
                        const timezone = getTimezone(selectedCity);
                        if (timezone === undefined) {
                            console.error('منطقة زمنية غير صالحة للمدينة:', selectedCity);
                            throw new Error('منطقة زمنية غير صالحة للمدينة: ' + selectedCity);
                        }

                        // الحصول على التاريخ الحالي
                        const now = new Date();

                        // حساب مواقيت الصلاة
                        const calculatedTimes = window.prayTimes.getTimes(
                            now,
                            [coordinates.lat, coordinates.lng],
                            timezone
                        );

                        if (!calculatedTimes) {
                            console.error('فشل في حساب مواقيت الصلاة للمدينة:', selectedCity);
                            throw new Error('فشل في حساب مواقيت الصلاة للمدينة: ' + selectedCity);
                        }

                        // تنسيق المواقيت
                        times = {
                            fajr: calculatedTimes.fajr,
                            sunrise: calculatedTimes.sunrise,
                            dhuhr: calculatedTimes.dhuhr,
                            asr: calculatedTimes.asr,
                            maghrib: calculatedTimes.maghrib,
                            isha: calculatedTimes.isha
                        };

                        // التحقق من صحة المواقيت
                        let isValid = true;
                        for (const prayer in times) {
                            if (!times[prayer] || times[prayer] === '--:--') {
                                console.error(`وقت ${prayer} غير صالح للمدينة ${selectedCity}: ${times[prayer]}`);
                                isValid = false;
                                break;
                            }
                        }

                        if (!isValid) {
                            console.error('مواقيت غير صالحة للمدينة:', selectedCity, times);
                            throw new Error('مواقيت غير صالحة للمدينة: ' + selectedCity);
                        }

                        // تحديث المواقيت العالمية
                        if (!window.prayerTimes) {
                            window.prayerTimes = {};
                        }

                        window.prayerTimes[selectedCity] = JSON.parse(JSON.stringify(times));

                        // تحديث المواقيت الثابتة أيضاً
                        if (!window.AMMAN_PRAYER_TIMES) {
                            window.AMMAN_PRAYER_TIMES = {};
                        }
                        window.AMMAN_PRAYER_TIMES[selectedCity] = JSON.parse(JSON.stringify(times));

                        console.log('تم حساب المواقيت بنجاح:', times);
                    } catch (error) {
                        console.error('خطأ في حساب المواقيت:', error);
                    }
                }

                // إذا لم تكن المواقيت متوفرة، استخدم المواقيت الثابتة
                if (!times && window.AMMAN_PRAYER_TIMES) {
                    console.log('استخدام المواقيت الثابتة...');
                    times = window.AMMAN_PRAYER_TIMES[selectedCity] || window.AMMAN_PRAYER_TIMES['Asia/Amman'];
                }

                // إذا لم تكن المواقيت متوفرة، استخدم مواقيت افتراضية
                if (!times) {
                    console.warn('استخدام مواقيت افتراضية...');
                    times = {
                        fajr: '04:00',
                        sunrise: '05:30',
                        dhuhr: '12:00',
                        asr: '15:30',
                        maghrib: '18:30',
                        isha: '20:00'
                    };
                }

                console.log('المواقيت المستخدمة للتحديث:', times);

                // تحديث عرض المواقيت في المستطيل الأفقي
                updatePrayerTimesDisplay(times, '24');

                // تحديث قائمة الصلوات القادمة
                if (typeof updateUpcomingPrayers === 'function') {
                    updateUpcomingPrayers();
                }

                // تحديث العد التنازلي
                if (typeof updateCountdown === 'function') {
                    updateCountdown();
                }

                // تحديث نص الصلاة القادمة
                if (typeof updateNextPrayerText === 'function') {
                    updateNextPrayerText();
                }

                // تحديث أي عناصر أخرى تعتمد على مواقيت الصلاة
                if (typeof displayRemainingPrayerTimes === 'function') {
                    displayRemainingPrayerTimes();
                }

                console.log('تم تحديث جميع عروض مواقيت الصلاة في التطبيق بنجاح');
            } catch (error) {
                console.error('خطأ في تحديث عروض مواقيت الصلاة:', error);
            }
        }

        // دالة لتحديث نص الصلاة القادمة
        function updateNextPrayerText() {
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

            // محاولة الحصول على المواقيت من window.prayerTimes أولاً
            let times = window.prayerTimes?.[currentCity];

            // إذا لم تكن متوفرة، حاول حساب المواقيت باستخدام مكتبة PrayTimes.js
            if (!times && window.prayTimes) {
                console.log('المواقيت غير متوفرة في window.prayerTimes، محاولة حساب المواقيت باستخدام مكتبة PrayTimes.js...');

                try {
                    // الحصول على طريقة الحساب المحفوظة
                    const savedMethod = localStorage.getItem('calculationMethod') || 'MWL';
                    const savedJuristicMethod = localStorage.getItem('juristicMethod') || 'Shafi';

                    // تعيين طريقة الحساب
                    window.prayTimes.setMethod(savedMethod);

                    // تعيين المذهب (للعصر)
                    const juristicMethodValue = savedJuristicMethod === 'Hanafi' ? 1 : 0;
                    window.prayTimes.adjust({asr: juristicMethodValue});

                    // الحصول على إحداثيات المدينة
                    const coordinates = getCityCoordinates(currentCity);

                    // الحصول على المنطقة الزمنية
                    const timezone = getTimezone(currentCity);

                    // الحصول على التاريخ الحالي
                    const now = new Date();

                    // حساب مواقيت الصلاة
                    const calculatedTimes = window.prayTimes.getTimes(
                        now,
                        [coordinates.lat, coordinates.lng],
                        timezone
                    );

                    console.log('تم حساب المواقيت باستخدام مكتبة PrayTimes.js مباشرة:', calculatedTimes);

                    // تنسيق المواقيت
                    times = {
                        fajr: calculatedTimes.fajr,
                        sunrise: calculatedTimes.sunrise,
                        dhuhr: calculatedTimes.dhuhr,
                        asr: calculatedTimes.asr,
                        maghrib: calculatedTimes.maghrib,
                        isha: calculatedTimes.isha
                    };

                    // تحديث المواقيت العالمية
                    if (!window.prayerTimes) {
                        window.prayerTimes = {};
                    }

                    window.prayerTimes[currentCity] = times;

                    // تحديث العرض
                    updatePrayerTimesDisplay(times, '24');
                } catch (error) {
                    console.error('خطأ في حساب المواقيت باستخدام مكتبة PrayTimes.js:', error);
                }
            }

            // إذا لم تكن متوفرة، استخدم المواقيت الثابتة كملاذ أخير
            if (!times && window.AMMAN_PRAYER_TIMES) {
                console.warn('فشل حساب المواقيت، استخدام المواقيت الثابتة...');
                times = window.AMMAN_PRAYER_TIMES[currentCity];
            }

            // إذا لم تكن المواقيت متوفرة في أي مصدر
            if (!times) {
                console.error('لا توجد مواقيت متاحة لتحديث نص الصلاة القادمة');

                // إنشاء مواقيت افتراضية
                times = {
                    fajr: '04:00',
                    sunrise: '05:30',
                    dhuhr: '12:00',
                    asr: '15:30',
                    maghrib: '18:30',
                    isha: '20:00'
                };

                console.warn('تم إنشاء مواقيت افتراضية:', times);
            }

            const now = new Date();
            const currentHours = now.getHours();
            const currentMinutes = now.getMinutes();
            const currentTimeInMinutes = currentHours * 60 + currentMinutes;

            console.log(`الوقت الحالي في updateNextPrayerText: ${currentHours}:${currentMinutes} (${currentTimeInMinutes} دقيقة)`);

            const prayers = [
                { name: 'الفجر', time: times.fajr },
                { name: 'الشروق', time: times.sunrise },
                { name: 'الظهر', time: times.dhuhr },
                { name: 'العصر', time: times.asr },
                { name: 'المغرب', time: times.maghrib },
                { name: 'العشاء', time: times.isha }
            ];

            console.log('مواقيت الصلوات في updateNextPrayerText:', prayers.map(p => `${p.name}: ${p.time}`));

            // البحث عن الصلاة القادمة
            let nextPrayer = null;
            let minTimeToNextPrayer = Infinity;

            for (const prayer of prayers) {
                if (!prayer.time) {
                    console.log(`تخطي صلاة ${prayer.name} لأن وقتها غير متوفر`);
                    continue;
                }

                const [hours, minutes] = prayer.time.split(':').map(Number);
                const prayerTimeInMinutes = hours * 60 + minutes;

                console.log(`صلاة ${prayer.name}: ${hours}:${minutes} (${prayerTimeInMinutes} دقيقة)`);

                // حساب الوقت المتبقي للصلاة القادمة
                let timeToNextPrayer = prayerTimeInMinutes - currentTimeInMinutes;

                // إذا كان الوقت سالباً (أي أن الصلاة قد مرت)، أضف 24 ساعة
                if (timeToNextPrayer < 0) {
                    timeToNextPrayer += 24 * 60; // 24 ساعة بالدقائق
                    console.log(`صلاة ${prayer.name} مرت، الوقت المتبقي بعد إضافة 24 ساعة: ${timeToNextPrayer} دقيقة`);
                } else {
                    console.log(`الوقت المتبقي لصلاة ${prayer.name}: ${timeToNextPrayer} دقيقة`);
                }

                // إذا كان هذا أقرب صلاة قادمة
                if (timeToNextPrayer < minTimeToNextPrayer) {
                    minTimeToNextPrayer = timeToNextPrayer;
                    nextPrayer = prayer;
                    console.log(`تم تحديث الصلاة القادمة إلى ${prayer.name} (${timeToNextPrayer} دقيقة)`);
                }
            }

            // التأكد من وجود صلاة قادمة
            if (!nextPrayer) {
                console.error('لم يتم العثور على صلاة قادمة في updateNextPrayerText!');
                nextPrayer = prayers[0]; // استخدام الفجر كاحتياطي
                console.log('تم استخدام صلاة الفجر كاحتياطي للصلاة القادمة');
            }

            // تحديث نص الصلاة القادمة
            const nextPrayerText = document.querySelector('.next-prayer-text');
            if (nextPrayerText && nextPrayer) {
                nextPrayerText.textContent = `ننتظر صلاة ${nextPrayer.name}`;
                console.log(`تم تحديث نص الصلاة القادمة: ننتظر صلاة ${nextPrayer.name}`);
            } else {
                console.error('لم يتم تحديث نص الصلاة القادمة: عنصر النص أو الصلاة القادمة غير موجود');
            }

            return nextPrayer;
        }

        // دالة لإعداد مستمع لزر تحديث مواقيت الصلاة
        function setupUpdatePrayerTimesButton() {
            const updatePrayerTimesBtn = document.getElementById('update-prayer-times');
            if (updatePrayerTimesBtn) {
                updatePrayerTimesBtn.addEventListener('click', async () => {
                    try {
                        // عرض إشعار
                        showNotification('جاري تحديث مواقيت الصلاة...', 'info');

                        // جلب أوقات الصلاة
                        await getPrayerTimes();

                        // تحديث العرض
                        displayRemainingPrayerTimes();
                        updateCountdown();
                        updateNextPrayerText();

                        // عرض إشعار
                        const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                        const savedMethod = localStorage.getItem('calculationMethod') || 'MWL';
                        const cityName = document.getElementById('selected-city')?.options[document.getElementById('selected-city')?.selectedIndex]?.text || currentCity;
                        const methodName = document.getElementById('calculation-method')?.options[document.getElementById('calculation-method')?.selectedIndex]?.text || savedMethod;

                        showNotification(`تم تحديث مواقيت الصلاة لمدينة ${cityName} بطريقة ${methodName}`, 'success');
                    } catch (error) {
                        console.error('خطأ في تحديث مواقيت الصلاة:', error);
                        showNotification('حدث خطأ أثناء تحديث مواقيت الصلاة', 'error');
                    }
                });
            }
        }

        // دالة لإعداد مستمعي الأحداث لإعدادات الأذان
        function setupAdhanListeners() {
            // الحصول على عناصر الأذان
            const enableAdhanCheckbox = document.getElementById('enable-adhan');
            const adhanVolumeSlider = document.getElementById('adhan-volume');
            const saveAdhanSettingsBtn = document.getElementById('save-adhan-settings');

            // التحقق من وجود العناصر
            if (!enableAdhanCheckbox || !adhanVolumeSlider || !saveAdhanSettingsBtn) {
                console.error('عناصر إعدادات الأذان غير موجودة');
                return;
            }

            // إضافة مستمع لزر حفظ إعدادات الأذان
            saveAdhanSettingsBtn.addEventListener('click', () => {
                // حفظ الإعدادات في التخزين المحلي
                const settings = {
                    enabled: enableAdhanCheckbox.checked,
                    volume: adhanVolumeSlider.value
                };

                localStorage.setItem('adhanSettings', JSON.stringify(settings));

                // عرض إشعار
                showNotification('تم حفظ إعدادات الأذان بنجاح', 'success');

                console.log('تم حفظ إعدادات الأذان:', settings);
            });
        }

        // دالة لتحميل إعدادات الأذان المحفوظة
        function loadAdhanSettings() {
            // الحصول على عناصر الأذان
            const enableAdhanCheckbox = document.getElementById('enable-adhan');
            const adhanVolumeSlider = document.getElementById('adhan-volume');

            // التحقق من وجود العناصر
            if (!enableAdhanCheckbox || !adhanVolumeSlider) {
                console.error('عناصر إعدادات الأذان غير موجودة');
                return;
            }

            // محاولة قراءة الإعدادات من التخزين المحلي
            try {
                const adhanSettings = JSON.parse(localStorage.getItem('adhanSettings') || '{}');

                // تعيين حالة تفعيل الأذان
                if (adhanSettings.enabled !== undefined) {
                    enableAdhanCheckbox.checked = adhanSettings.enabled;
                }

                // تعيين مستوى صوت الأذان
                if (adhanSettings.volume !== undefined) {
                    adhanVolumeSlider.value = adhanSettings.volume;
                }

                console.log('تم تحميل إعدادات الأذان:', adhanSettings);
            } catch (error) {
                console.error('خطأ في قراءة إعدادات الأذان:', error);
            }
        }

        // دالة لعرض الإشعارات
        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.getElementById('notification');
            if (!notification) return;

            // إزالة جميع الأصناف السابقة
            notification.className = 'notification';

            // إضافة الصنف المناسب
            notification.classList.add(type);

            // تعيين الرسالة
            notification.textContent = message;

            // إظهار الإشعار
            notification.style.display = 'block';

            // إخفاء الإشعار بعد المدة المحددة
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    notification.style.display = 'none';
                    notification.style.opacity = '1';
                }, 300);
            }, duration);

            // إذا كانت الرسالة تتعلق بتطبيق المواقيت المعدلة يدوياً
            if (message.includes('تم تطبيق المواقيت المعدلة يدوياً') ||
                message.includes('تم حفظ وتطبيق المواقيت المعدلة يدوياً') ||
                message.includes('تم تعديل وقت')) {
                // تأكيد تطبيق المواقيت المعدلة يدوياً مرة أخرى بعد ظهور الإشعار
                setTimeout(() => {
                    if (manualPrayerTimesEnabled && manualPrayerTimes) {
                        const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                        if (manualPrayerTimes[currentCity]) {
                            console.log('إعادة تطبيق المواقيت المعدلة يدوياً بعد ظهور الإشعار...');

                            // نسخ المواقيت المعدلة يدوياً إلى متغير مؤقت
                            const manualTimes = JSON.parse(JSON.stringify(manualPrayerTimes[currentCity]));

                            // تحديث المواقيت العالمية
                            if (!window.prayerTimes) {
                                window.prayerTimes = {};
                            }

                            // تحديث المواقيت العالمية للمدينة الحالية
                            window.prayerTimes[currentCity] = manualTimes;

                            // تحديث المواقيت الثابتة
                            if (!window.AMMAN_PRAYER_TIMES) {
                                window.AMMAN_PRAYER_TIMES = {};
                            }

                            window.AMMAN_PRAYER_TIMES[currentCity] = manualTimes;

                            // تحديث العرض
                            updatePrayerTimesDisplay(manualTimes, '24');

                            console.log('تم إعادة تطبيق المواقيت المعدلة يدوياً بعد ظهور الإشعار');
                        }
                    }
                }, 100);
            }
        }

        // دالة تعتيم الشاشة
        function startScreenDimming(prayer, duration) {
            // إنشاء طبقة التعتيم إذا لم تكن موجودة
            let dimOverlay = document.getElementById('dim-overlay');
            if (!dimOverlay) {
                dimOverlay = document.createElement('div');
                dimOverlay.id = 'dim-overlay';
                dimOverlay.style.position = 'fixed';
                dimOverlay.style.top = '0';
                dimOverlay.style.left = '0';
                dimOverlay.style.width = '100%';
                dimOverlay.style.height = '100%';
                dimOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                dimOverlay.style.transition = 'opacity 1s';
                dimOverlay.style.zIndex = '9999';
                document.body.appendChild(dimOverlay);
            }

            // إظهار طبقة التعتيم
            dimOverlay.style.opacity = '1';

            // إزالة التعتيم بعد المدة المحددة
            setTimeout(() => {
                dimOverlay.style.opacity = '0';
                setTimeout(() => {
                    dimOverlay.remove();
                }, 1000);
            }, duration * 60 * 1000);
        }

        // دالة لتحديث الساعة التناظرية
        function updateAnalogClock() {
            const now = new Date();
            const hours = now.getHours() % 12;
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            const milliseconds = now.getMilliseconds();

            const hourHand = document.querySelector('.hour-hand');
            const minuteHand = document.querySelector('.minute-hand');
            const secondHand = document.querySelector('.second-hand');

            const hourDeg = (hours * 30) + (minutes * 0.5) + (seconds * 0.008333);
            const minuteDeg = (minutes * 6) + (seconds * 0.1) + (milliseconds * 0.0001);
            const secondDeg = (seconds * 6) + (milliseconds * 0.006);

            hourHand.style.transform = `translateX(-50%) rotate(${hourDeg}deg)`;
            minuteHand.style.transform = `translateX(-50%) rotate(${minuteDeg}deg)`;
            secondHand.style.transform = `translateX(-50%) rotate(${secondDeg}deg)`;
        }

        // دالة لتحديث الساعة الرقمية
        function updateDigitalClock() {
            const now = new Date();
            const timeFormat = document.getElementById('time-format-select')?.value || '24';
            let hours = now.getHours();
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const milliseconds = String(Math.floor(now.getMilliseconds() / 10)).padStart(2, '0');

            if (timeFormat === '12') {
                const period = hours >= 12 ? 'م' : 'ص';
                hours = hours % 12 || 12;
                hours = String(hours).padStart(2, '0');
                document.querySelector('.digital-clock').innerHTML = `<span style="color: #40E0D0">${hours}:${minutes}:${seconds}.${milliseconds}</span> ${period}`;
            } else {
                hours = String(hours).padStart(2, '0');
                document.querySelector('.digital-clock').innerHTML = `<span style="color: #40E0D0">${hours}:${minutes}:${seconds}.${milliseconds}</span>`;
            }
        }

        // تحديث الساعة كل 10 ميلي ثانية
        function startClockUpdate() {
            updateAnalogClock();
            updateDigitalClock();
            setInterval(() => {
                updateAnalogClock();
                updateDigitalClock();
            }, 10);
        }

        // دالة لتحديث حالة الطقس
        function updateWeather() {
            console.log('جاري تحديث حالة الطقس...');

            const weatherImg = document.querySelector('.weather-display img');
            const temperatureDiv = document.querySelector('.weather-display .temperature');
            const descriptionDiv = document.querySelector('.weather-display .description');

            if (!weatherImg || !temperatureDiv || !descriptionDiv) {
                console.warn('عناصر حالة الطقس غير متوفرة');
                return;
            }

            // الحصول على المدينة الحالية
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            let cityName = currentCity.split('/').pop();

            // تعيين اسم المدينة باللغة الإنجليزية للبحث
            const citySearchMapping = {
                'Amman': 'Amman,JO',
                'Riyadh': 'Riyadh,SA',
                'Dubai': 'Dubai,AE',
                'Makkah': 'Mecca,SA',
                'Jeddah': 'Jeddah,SA'
            };

            // الحصول على اسم المدينة باللغة العربية للعرض
            const cityDisplayMapping = {
                'Amman': 'عمان',
                'Riyadh': 'الرياض',
                'Dubai': 'دبي',
                'Makkah': 'مكة المكرمة',
                'Jeddah': 'جدة'
            };

            const searchCity = citySearchMapping[cityName] || 'Amman,JO';
            const displayCity = cityDisplayMapping[cityName] || 'عمان';

            console.log('البحث عن الطقس لمدينة:', searchCity);

            // استخدام OpenWeatherMap API لجلب حالة الطقس
            const apiKey = '822b29fcd49861b4a41912c2cdd68f40';
            const url = `https://api.openweathermap.org/data/2.5/weather?q=${searchCity}&appid=${apiKey}&units=metric&lang=ar`;

            // عرض حالة التحميل
            temperatureDiv.textContent = "جاري التحميل...";
            descriptionDiv.textContent = displayCity;

            // تنفيذ الطلب باستخدام fetch
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('خطأ في جلب حالة الطقس');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('تم استلام بيانات الطقس:', data);

                    // تحديث حالة الطقس
                    const iconCode = data.weather[0].icon;
                    const temperature = Math.round(data.main.temp);
                    const description = data.weather[0].description;

                    console.log('درجة الحرارة:', temperature, 'الوصف:', description);

                    weatherImg.src = `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
                    temperatureDiv.textContent = `${temperature}°C`;
                    descriptionDiv.textContent = `${description} - ${displayCity}`;

                    // تخزين البيانات محلياً
                    localStorage.setItem('weatherData', JSON.stringify(data));
                    localStorage.setItem('weatherTimestamp', Date.now());

                    console.log('تم تحديث حالة الطقس بنجاح');
                })
                .catch(error => {
                    console.error('خطأ في تحديث حالة الطقس:', error);

                    // محاولة استخدام البيانات المخزنة
                    const cachedWeather = localStorage.getItem('weatherData');
                    const timestamp = localStorage.getItem('weatherTimestamp');
                    const oneHour = 60 * 60 * 1000; // ساعة بالميلي ثانية

                    if (cachedWeather && timestamp && (Date.now() - parseInt(timestamp)) < oneHour) {
                        try {
                            console.log('استخدام بيانات الطقس المخزنة');
                            const data = JSON.parse(cachedWeather);
                            const iconCode = data.weather[0].icon;
                            const temperature = Math.round(data.main.temp);
                            const description = data.weather[0].description;

                            weatherImg.src = `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
                            temperatureDiv.textContent = `${temperature}°C`;
                            descriptionDiv.textContent = `${description} - ${displayCity}`;

                            console.log('تم استخدام حالة الطقس المخزنة');
                        } catch (err) {
                            console.error('خطأ في قراءة البيانات المخزنة:', err);
                            setDefaultWeather(temperatureDiv, descriptionDiv, displayCity);
                        }
                    } else {
                        setDefaultWeather(temperatureDiv, descriptionDiv, displayCity);
                    }
                });
        }

        // دالة لعرض حالة طقس افتراضية
        function setDefaultWeather(temperatureDiv, descriptionDiv, city) {
            temperatureDiv.textContent = '--°C';
            descriptionDiv.textContent = `${city} - غير متوفر`;
        }

        // تحديث حالة الطقس عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateWeather();

            // تحديث حالة الطقس كل ساعة
            setInterval(updateWeather, 3600000);
        });

        // تحديث حالة الطقس عند تغيير المدينة
        document.getElementById('city-select')?.addEventListener('change', function() {
            updateWeather();
        });

        // تحديث الساعة عند تغيير نظام الوقت
        document.getElementById('time-format-select')?.addEventListener('change', function() {
            updateDigitalClock();
            updatePrayerTimesDisplay(window.prayerTimes?.[localStorage.getItem('selectedCity') || 'Asia/Amman'] || {}, this.value);
        });

        // تهيئة مكتبة PrayTimes.js عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة مكتبة PrayTimes.js
            if (typeof prayTimes !== 'undefined') {
                window.prayTimes = prayTimes;
                console.log('تم تهيئة مكتبة PrayTimes.js بنجاح');

                // تحديث مواقيت الصلاة باستخدام المكتبة
                setTimeout(function() {
                    try {
                        // تحديث إجباري لمواقيت الصلاة عند تحميل الصفحة
                        forceUpdatePrayerTimes();
                        console.log('تم تحديث مواقيت الصلاة عند تحميل الصفحة');
                    } catch (error) {
                        console.error('خطأ في تحديث مواقيت الصلاة عند تحميل الصفحة:', error);
                    }
                }, 1000);
            } else {
                console.error('مكتبة PrayTimes.js غير متوفرة');
                console.warn('سيتم استخدام المواقيت الثابتة');
            }
        });

        // إضافة الكود الجديد للتحكم في حجم الشاشة والاتجاه
        document.addEventListener('DOMContentLoaded', function() {
            // أحجام الشاشات المختلفة
            const screenSizes = {
                mobile: { width: '375px', height: '667px' },
                tablet: { width: '768px', height: '1024px' },
                laptop: { width: '1366px', height: '768px' },
                tv: { width: '1920px', height: '1080px' }
            };

            // التحكم في أزرار حجم الشاشة
            const sizeButtons = document.querySelectorAll('.screen-size-button');
            sizeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const size = this.dataset.size;
                    const dimensions = screenSizes[size];

                    // تحديث حجم العرض
                    document.body.style.width = dimensions.width;
                    document.body.style.height = dimensions.height;

                    // تحديث الأزرار النشطة
                    sizeButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // حفظ الإعداد
                    localStorage.setItem('screenSize', size);
                });
            });

            // التحكم في أزرار الاتجاه
            const orientationButtons = document.querySelectorAll('.orientation-button');
            orientationButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const orientation = this.dataset.orientation;

                    // تحديث اتجاه العرض
                    if (orientation === 'portrait') {
                        document.body.style.flexDirection = 'column';
                    } else {
                        document.body.style.flexDirection = 'row';
                    }

                    // تحديث الأزرار النشطة
                    orientationButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // حفظ الإعداد
                    localStorage.setItem('orientation', orientation);
                });
            });

            // استعادة الإعدادات المحفوظة
            const savedSize = localStorage.getItem('screenSize');
            const savedOrientation = localStorage.getItem('orientation');

            if (savedSize) {
                const button = document.querySelector(`[data-size="${savedSize}"]`);
                if (button) button.click();
            }

            if (savedOrientation) {
                const button = document.querySelector(`[data-orientation="${savedOrientation}"]`);
                if (button) button.click();
            }
        });

        // إضافة وظيفة تغيير الاتجاه
        function initializeOrientation() {
            const container = document.querySelector('.container');
            const orientationButtons = document.querySelectorAll('.orientation-button');

            // استرجاع الاتجاه المحفوظ
            const savedOrientation = localStorage.getItem('orientation') || 'portrait';
            container.className = `container ${savedOrientation}`;

            // تحديث حالة الأزرار
            orientationButtons.forEach(button => {
                if (button.dataset.orientation === savedOrientation) {
                    button.classList.add('active');
                }

                button.addEventListener('click', () => {
                    // إزالة الحالة النشطة من جميع الأزرار
                    orientationButtons.forEach(btn => btn.classList.remove('active'));

                    // تفعيل الزر المختار
                    button.classList.add('active');

                    // تحديث اتجاه العرض
                    const newOrientation = button.dataset.orientation;
                    container.className = `container ${newOrientation}`;

                    // حفظ الاتجاه في localStorage
                    localStorage.setItem('orientation', newOrientation);
                });
            });
        }

        // تشغيل الوظيفة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            initializeOrientation();
        });

        // إضافة الكود الجديد لإدارة اسم المسجد
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء عنصر عرض اسم المسجد
            const mosqueNameDisplay = document.createElement('div');
            mosqueNameDisplay.className = 'mosque-name-display';
            document.body.appendChild(mosqueNameDisplay);

            // استرجاع القيم المحفوظة
            const savedMosqueName = localStorage.getItem('mosqueName') || 'مسجد';
            const savedFontSize = localStorage.getItem('mosqueFontSize') || '32';
            const savedColor = localStorage.getItem('mosqueColor') || '#FFFFFF';

            // تعيين القيم المحفوظة
            document.getElementById('mosque-name-input').value = savedMosqueName || '';
            document.getElementById('mosque-font-size').textContent = savedFontSize;
            document.getElementById('mosque-text-color').value = savedColor;
            updateMosqueDisplay();

            // مستمع لتغيير اسم المسجد
            document.getElementById('mosque-name-input').addEventListener('input', function() {
                localStorage.setItem('mosqueName', this.value);
                updateMosqueDisplay();
            });

            // مستمعات لأزرار تغيير حجم الخط
            document.getElementById('increase-mosque-text').addEventListener('click', function() {
                let size = parseInt(document.getElementById('mosque-font-size').textContent);
                if (size < 72) {
                    size += 2;
                    document.getElementById('mosque-font-size').textContent = size;
                    localStorage.setItem('mosqueFontSize', size);
                    updateMosqueDisplay();
                }
            });

            document.getElementById('decrease-mosque-text').addEventListener('click', function() {
                let size = parseInt(document.getElementById('mosque-font-size').textContent);
                if (size > 16) {
                    size -= 2;
                    document.getElementById('mosque-font-size').textContent = size;
                    localStorage.setItem('mosqueFontSize', size);
                    updateMosqueDisplay();
                }
            });

            // مستمع لتغيير لون الخط
            document.getElementById('mosque-text-color').addEventListener('change', function() {
                localStorage.setItem('mosqueColor', this.value);
                updateMosqueDisplay();
            });

            // دالة تحديث عرض اسم المسجد
            function updateMosqueDisplay() {
                const mosqueName = document.getElementById('mosque-name-input').value || 'مسجد';
                const size = document.getElementById('mosque-font-size').textContent;
                const color = document.getElementById('mosque-text-color').value;

                mosqueNameDisplay.style.fontSize = size;
                mosqueNameDisplay.style.color = color;
                mosqueNameDisplay.textContent = mosqueName;
            }
        });

        // إضافة الكود الجديد للتحكم في حجم الشاشة
        document.addEventListener('DOMContentLoaded', function() {
            const decreaseBtn = document.getElementById('decrease-screen-size-control');
            const increaseBtn = document.getElementById('increase-screen-size-control');
            const sizeValue = document.getElementById('screen-size-value-display');
            const presetButtons = document.querySelectorAll('.preset-size-btn');

            // استرجاع الحجم المحفوظ
            let currentSize = parseInt(localStorage.getItem('screenSize')) || 100;
            updateScreenSize(currentSize);

            // تحديث حجم الشاشة
            function updateScreenSize(size) {
                currentSize = Math.max(50, Math.min(200, size)); // تحديد الحد الأدنى والأقصى
                document.body.style.transform = `scale(${currentSize / 100})`;
                document.body.style.transformOrigin = 'center top';
                sizeValue.textContent = `${currentSize}%`;
                localStorage.setItem('screenSize', currentSize);

                // تحديث حالة الأزرار المحددة مسبقاً
                presetButtons.forEach(btn => {
                    if (parseInt(btn.dataset.size) === currentSize) {
                        btn.classList.add('active');
                    } else {
                        btn.classList.remove('active');
                    }
                });
            }

            // مستمعي الأحداث لأزرار التكبير والتصغير
            decreaseBtn.addEventListener('click', () => {
                updateScreenSize(currentSize - 5);
            });

            increaseBtn.addEventListener('click', () => {
                updateScreenSize(currentSize + 5);
            });

            // مستمعي الأحداث لأزرار الحجم المحدد مسبقاً
            presetButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    const size = parseInt(btn.dataset.size);
                    updateScreenSize(size);
                });
            });
        });

        // كود JavaScript الأساسي للصفحة

        // كود التكبير والتصغير
        document.addEventListener('DOMContentLoaded', function() {
            const zoomInBtn = document.getElementById('zoom-in-control');
            const zoomOutBtn = document.getElementById('zoom-out-control');
            const zoomLevelDisplay = document.getElementById('zoom-level-display');

            // استرجاع مستوى التكبير المحفوظ
            let zoomLevel = localStorage.getItem('zoomLevel') || 100;
            applyZoom(zoomLevel);

            // تطبيق التكبير
            function applyZoom(level) {
                zoomLevel = Math.max(50, Math.min(200, Number(level)));
                document.body.style.transform = `scale(${zoomLevel / 100})`;
                zoomLevelDisplay.textContent = `${zoomLevel}%`;
                localStorage.setItem('zoomLevel', zoomLevel);
            }

            // أزرار التكبير والتصغير
            zoomInBtn.addEventListener('click', function() {
                applyZoom(Number(zoomLevel) + 10);
            });

            zoomOutBtn.addEventListener('click', function() {
                applyZoom(Number(zoomLevel) - 10);
            });
        });

        // كود تعديل مواقيت الصلاة
        document.addEventListener('DOMContentLoaded', function() {
            const prayerTimeInputs = {
                fajr: document.getElementById('fajr-time-input'),
                sunrise: document.getElementById('sunrise-time-input'),
                dhuhr: document.getElementById('dhuhr-time-input'),
                asr: document.getElementById('asr-time-input'),
                maghrib: document.getElementById('maghrib-time-input'),
                isha: document.getElementById('isha-time-input')
            };

            const saveButton = document.getElementById('save-prayer-times');
            const resetButton = document.getElementById('reset-prayer-times');

            // استرجاع المواقيت المحفوظة
            function loadSavedPrayerTimes() {
                const savedTimes = localStorage.getItem('customPrayerTimes');
                if (savedTimes) {
                    const times = JSON.parse(savedTimes);
                    Object.keys(prayerTimeInputs).forEach(prayer => {
                        if (times[prayer]) {
                            prayerTimeInputs[prayer].value = times[prayer];
                        }
                    });
                    // تحديث العرض مباشرة عند تحميل المواقيت المحفوظة
                    updatePrayerTimesDisplay(times);
                }
            }

            // حفظ المواقيت المخصصة
            function saveCustomPrayerTimes() {
                console.log('حفظ المواقيت المخصصة...');

                try {
                    const customTimes = {};
                    let hasValidTimes = false;

                    // جمع المواقيت من حقول الإدخال
                    Object.keys(prayerTimeInputs).forEach(prayer => {
                        if (prayerTimeInputs[prayer] && prayerTimeInputs[prayer].value) {
                            const timeValue = prayerTimeInputs[prayer].value.trim();

                            // التحقق من صحة الوقت
                            const timeRegex = /^([01]?[0-9]|2[0-3]):([0-5][0-9])$/;
                            if (timeRegex.test(timeValue)) {
                                customTimes[prayer] = timeValue;
                                hasValidTimes = true;
                                console.log(`وقت صلاة ${prayer}: ${timeValue}`);
                            } else {
                                console.error(`وقت غير صالح لصلاة ${prayer}: ${timeValue}`);
                                alert(`وقت غير صالح لصلاة ${prayer}: ${timeValue}\nيجب أن يكون الوقت بتنسيق 24 ساعة (مثال: 05:30)`);
                                return;
                            }
                        }
                    });

                    if (!hasValidTimes) {
                        console.error('لم يتم إدخال أي مواقيت صالحة');
                        alert('يرجى إدخال مواقيت الصلوات أولاً');
                        return;
                    }

                    // الحصول على المدينة الحالية
                    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                    console.log('المدينة الحالية:', currentCity);

                    // تحويل اسم المدينة من التنسيق القديم إلى التنسيق الجديد
                    let cityName;
                    if (currentCity === 'Asia/Amman') {
                        cityName = 'عمان';
                    } else if (currentCity === 'Asia/Riyadh') {
                        cityName = 'الرياض';
                    } else if (currentCity === 'Asia/Makkah') {
                        cityName = 'مكة المكرمة';
                    } else if (currentCity === 'Africa/Cairo') {
                        cityName = 'القاهرة';
                    } else {
                        // إذا لم يتم العثور على المدينة، استخدم عمان كاحتياطي
                        cityName = 'عمان';
                    }

                    // تعديل مواقيت الصلاة في PrayerManager
                    let allSuccess = true;
                    Object.keys(customTimes).forEach(prayer => {
                        const time = customTimes[prayer];
                        const success = PrayerManager.setManualAdjustment(cityName, prayer, time);

                        if (success) {
                            console.log(`تم تعديل وقت ${prayer} إلى ${time} في PrayerManager بنجاح`);
                        } else {
                            console.error(`فشل في تعديل وقت ${prayer} في PrayerManager`);
                            allSuccess = false;
                        }
                    });

                    if (allSuccess) {
                        // تفعيل التعديلات اليدوية
                        PrayerManager.settings.manualAdjustmentsEnabled = true;
                        PrayerManager.saveSettings();
                        console.log('تم تفعيل التعديلات اليدوية في PrayerManager');
                    }

                    // تخزين المواقيت المخصصة في localStorage
                    localStorage.setItem('customPrayerTimes', JSON.stringify(customTimes));
                    console.log('تم حفظ المواقيت المخصصة في localStorage:', customTimes);

                    // تحديث المتغير العام
                    if (!window.prayerTimes) {
                        window.prayerTimes = {};
                    }
                    window.prayerTimes[currentCity] = customTimes;
                    console.log('تم تحديث المتغير العام window.prayerTimes:', window.prayerTimes);

                    // تحديث العرض
                    updatePrayerTimesDisplay(customTimes);
                    console.log('تم تحديث عرض مواقيت الصلاة');

                    // تحديث مواقيت الصلاة
                    if (typeof window.getPrayerTimes === 'function') {
                        window.getPrayerTimes();
                        console.log('تم تحديث مواقيت الصلاة باستخدام window.getPrayerTimes()');
                    }

                    // إظهار رسالة تأكيد
                    alert('تم حفظ مواقيت الصلوات بنجاح');

                    return true;
                } catch (error) {
                    console.error('خطأ في حفظ المواقيت المخصصة:', error);
                    alert('حدث خطأ أثناء حفظ مواقيت الصلوات');
                    return false;
                }
            }

            // إعادة تعيين المواقيت
            function resetPrayerTimes() {
                console.log('إعادة تعيين مواقيت الصلوات...');

                try {
                    // الحصول على المدينة الحالية
                    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                    console.log('المدينة الحالية:', currentCity);

                    // تحويل اسم المدينة من التنسيق القديم إلى التنسيق الجديد
                    let cityName;
                    if (currentCity === 'Asia/Amman') {
                        cityName = 'عمان';
                    } else if (currentCity === 'Asia/Riyadh') {
                        cityName = 'الرياض';
                    } else if (currentCity === 'Asia/Makkah') {
                        cityName = 'مكة المكرمة';
                    } else if (currentCity === 'Africa/Cairo') {
                        cityName = 'القاهرة';
                    } else {
                        // إذا لم يتم العثور على المدينة، استخدم عمان كاحتياطي
                        cityName = 'عمان';
                    }

                    // حذف التعديلات اليدوية من PrayerManager
                    if (PrayerManager.clearManualAdjustments) {
                        const success = PrayerManager.clearManualAdjustments(cityName);
                        if (success) {
                            console.log(`تم حذف التعديلات اليدوية لمدينة ${cityName} من PrayerManager بنجاح`);
                        } else {
                            console.warn(`لم يتم العثور على تعديلات يدوية لمدينة ${cityName} في PrayerManager`);
                        }
                    }

                    // تعطيل التعديلات اليدوية
                    PrayerManager.settings.manualAdjustmentsEnabled = false;
                    PrayerManager.saveSettings();
                    console.log('تم تعطيل التعديلات اليدوية في PrayerManager');

                    // حذف المواقيت المخصصة من localStorage
                    localStorage.removeItem('customPrayerTimes');
                    console.log('تم حذف المواقيت المخصصة من localStorage');

                    // إعادة تعيين حقول الإدخال
                    Object.values(prayerTimeInputs).forEach(input => {
                        if (input) {
                            input.value = '';
                        }
                    });
                    console.log('تم إعادة تعيين حقول الإدخال');

                    // إعادة تحميل المواقيت الأصلية
                    if (typeof window.getPrayerTimes === 'function') {
                        window.getPrayerTimes().then(times => {
                            console.log('تم إعادة تحميل المواقيت الأصلية:', times);

                            // تحديث العرض
                            updatePrayerTimesDisplay(times);
                            console.log('تم تحديث عرض مواقيت الصلاة');

                            // إظهار رسالة تأكيد
                            alert('تم إعادة تعيين مواقيت الصلوات');
                        }).catch(error => {
                            console.error('خطأ في إعادة تحميل المواقيت الأصلية:', error);

                            // محاولة تحديث المواقيت باستخدام forceUpdatePrayerTimes
                            if (typeof forceUpdatePrayerTimes === 'function') {
                                const times = forceUpdatePrayerTimes();
                                console.log('تم إعادة تحميل المواقيت الأصلية باستخدام forceUpdatePrayerTimes:', times);

                                // إظهار رسالة تأكيد
                                alert('تم إعادة تعيين مواقيت الصلوات');
                            } else {
                                alert('حدث خطأ أثناء إعادة تحميل المواقيت الأصلية');
                            }
                        });
                    } else if (typeof forceUpdatePrayerTimes === 'function') {
                        const times = forceUpdatePrayerTimes();
                        console.log('تم إعادة تحميل المواقيت الأصلية باستخدام forceUpdatePrayerTimes:', times);

                        // إظهار رسالة تأكيد
                        alert('تم إعادة تعيين مواقيت الصلوات');
                    } else {
                        console.error('لا توجد دالة لإعادة تحميل المواقيت الأصلية');
                        alert('حدث خطأ أثناء إعادة تعيين مواقيت الصلوات');
                    }

                    return true;
                } catch (error) {
                    console.error('خطأ في إعادة تعيين مواقيت الصلوات:', error);
                    alert('حدث خطأ أثناء إعادة تعيين مواقيت الصلوات');
                    return false;
                }
            }

            // تحديث عرض المواقيت في المستطيل الأفقي
            function updatePrayerTimesDisplay(times) {
                const timeFormat = document.getElementById('time-format-select')?.value || '24';
                const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

                // تحديث المتغير العام
                window.prayerTimes = {
                    [currentCity]: times
                };

                // تحديث العرض في المستطيل الأفقي
                Object.keys(times).forEach(prayer => {
                    const timeElement = document.getElementById(`${prayer}-time`);
                    if (timeElement) {
                        const time = times[prayer];
                        if (timeFormat === '12') {
                            const [hours, minutes] = time.split(':').map(Number);
                            const period = hours >= 12 ? 'م' : 'ص';
                            const displayHours = hours % 12 || 12;
                            timeElement.innerHTML = `<span style="color: #40E0D0">${String(displayHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}</span> ${period}`;
                        } else {
                            timeElement.innerHTML = `<span style="color: #40E0D0">${time}</span>`;
                        }
                    }
                });

                // تحديث العد التنازلي
                updateCountdown();
                // تحديث قائمة الصلوات القادمة
                displayRemainingPrayerTimes();
            }

            // إضافة مستمعي الأحداث إذا كانت العناصر موجودة
            if (saveButton) {
                saveButton.addEventListener('click', saveCustomPrayerTimes);
            }
            if (resetButton) {
                resetButton.addEventListener('click', resetPrayerTimes);
            }

            // تحميل المواقيت المحفوظة عند فتح الإعدادات
            loadSavedPrayerTimes();

            // تحديث المواقيت عند تغيير أي قيمة
            Object.values(prayerTimeInputs).forEach(input => {
                input.addEventListener('input', function() {
                    const customTimes = {};
                    Object.keys(prayerTimeInputs).forEach(prayer => {
                        if (prayerTimeInputs[prayer].value) {
                            customTimes[prayer] = prayerTimeInputs[prayer].value;
                        }
                    });
                    updatePrayerTimesDisplay(customTimes);
                });
            });

            // تحديث المواقيت كل دقيقة
            setInterval(() => {
                const customTimes = localStorage.getItem('customPrayerTimes');
                if (customTimes) {
                    updatePrayerTimesDisplay(JSON.parse(customTimes));
                } else {
                    getPrayerTimes().then(times => {
                        updatePrayerTimesDisplay(times);
                    });
                }
            }, 60000);
        });

        // إضافة مستمع لتغيير طريقة الحساب
        document.getElementById('calculation-method')?.addEventListener('change', function() {
            const selectedMethod = this.value;
            console.log('تم تغيير طريقة الحساب إلى:', selectedMethod);

            // تحديث طريقة الحساب في localStorage
            localStorage.setItem('calculationMethod', selectedMethod);

            // إعادة حساب المواقيت باستخدام الطريقة الجديدة
            getPrayerTimes().then(times => {
                // تحديث العرض
                updatePrayerTimesDisplay(times);

                // تحديث قائمة الصلوات القادمة
                displayRemainingPrayerTimes();

                // تحديث العد التنازلي
                updateCountdown();

                // إظهار رسالة تأكيد
                alert('تم تحديث مواقيت الصلوات حسب الطريقة الجديدة');
            });
        });

        // إضافة مستمع لتغيير مدة التعتيم
        document.getElementById('save-darkness-times').addEventListener('click', function() {
            const darknessTimes = {
                fajr: parseInt(document.getElementById('fajr-darkness').value) || 0,
                dhuhr: parseInt(document.getElementById('dhuhr-darkness').value) || 0,
                asr: parseInt(document.getElementById('asr-darkness').value) || 0,
                maghrib: parseInt(document.getElementById('maghrib-darkness').value) || 0,
                isha: parseInt(document.getElementById('isha-darkness').value) || 0
            };
            localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
            alert('تم حفظ مدة التعتيم بنجاح');
        });

        // إضافة مستمع لتحميل مدة التعتيم
        document.addEventListener('DOMContentLoaded', function() {
            const darknessTimes = localStorage.getItem('darknessTimes');
            if (darknessTimes) {
                const times = JSON.parse(darknessTimes);
                Object.keys(times).forEach(prayer => {
                    const input = document.getElementById(`${prayer}-darkness`);
                    if (input) {
                        input.value = times[prayer];
                    }
                });
            }
        });

        // تحميل مواقيت الصلوات المحفوظة عند فتح الإعدادات
        function loadSavedPrayerTimes() {
            const savedTimes = JSON.parse(localStorage.getItem('manualPrayerTimes') || '{}');
            const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha', 'sunrise'];

            prayers.forEach(prayer => {
                const input = document.getElementById(`${prayer}-time`);
                if (input && savedTimes[prayer]) {
                    input.value = savedTimes[prayer];
                }
            });
        }

        // حفظ مواقيت الصلوات
        function savePrayerTime(prayer) {
            const time = document.getElementById(`${prayer}-time-input`).value;
            const prayerTimes = JSON.parse(localStorage.getItem('prayerTimes') || '{}');
            prayerTimes[prayer] = time;
            localStorage.setItem('prayerTimes', JSON.stringify(prayerTimes));

            // تحديث العرض في جميع الأماكن
            const tableElement = document.getElementById(`${prayer}-time`);
            if (tableElement) {
                tableElement.textContent = time;
            }

            const horizontalElement = document.querySelector(`.prayer-time[data-prayer="${prayer}"]`);
            if (horizontalElement) {
                horizontalElement.textContent = time;
            }

            showNotification(`تم حفظ وقت صلاة ${prayer}`, 'success');
            updatePrayerTimes();
        }

        // إعادة تعيين المواقيت
        function resetPrayerTimes() {
            localStorage.removeItem('manualPrayerTimes');
            loadSavedPrayerTimes();
            updatePrayerTimes();
        }

        // مزامنة المواقيت مع الخادم
        async function syncPrayerTimes() {
            try {
                const response = await fetch(`/api/prayer-times?city=${localStorage.getItem('selectedCity')}`);
                const times = await response.json();

                localStorage.setItem('manualPrayerTimes', JSON.stringify(times));
                loadSavedPrayerTimes();
                updatePrayerTimes();

                alert('تم مزامنة مواقيت الصلوات بنجاح');
            } catch (error) {
                console.error('خطأ في مزامنة مواقيت الصلوات:', error);
                alert('حدث خطأ أثناء مزامنة مواقيت الصلوات');
            }
        }

        // تحديث مواقيت الصلوات في جميع أجزاء الواجهة
        function updatePrayerTimes() {
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            const times = window.prayerTimes?.[currentCity];

            if (!times) {
                console.warn('أوقات الصلوات غير متوفرة');
                return;
            }

            const prayers = [
                { name: 'الفجر', time: times.fajr, id: 'fajr' },
                { name: 'الشروق', time: times.sunrise, id: 'sunrise' },
                { name: 'الظهر', time: times.dhuhr, id: 'dhuhr' },
                { name: 'العصر', time: times.asr, id: 'asr' },
                { name: 'المغرب', time: times.maghrib, id: 'maghrib' },
                { name: 'العشاء', time: times.isha, id: 'isha' }
            ];

            // تحديث العرض في المستطيل الأفقي
            prayers.forEach(prayer => {
                if (prayer.id === 'sunrise') return; // تخطي وقت الشروق

                // تحديث العرض في المستطيل الأفقي
                const horizontalElement = document.querySelector(`.prayer-time[data-prayer="${prayer.id}"]`);
                if (horizontalElement) {
                    horizontalElement.textContent = prayer.time;
                }

                // تحديث العرض في قسم الإعدادات
                const settingsInput = document.getElementById(`${prayer.id}-time-input`);
                if (settingsInput) {
                    settingsInput.value = prayer.time;
                }
            });

            // تحديث العرض في جدول المواقيت
            const prayerTimesContainer = document.getElementById('prayer-times');
            if (prayerTimesContainer) {
                prayerTimesContainer.innerHTML = prayers.map(prayer => `
                    <div class="prayer-time">
                        <span class="prayer-name">${prayer.name}</span>
                        <span class="prayer-time-display" id="${prayer.id}-time-display">${prayer.time}</span>
                        <button class="edit-prayer-btn" data-prayer="${prayer.id}">تعديل</button>
                    </div>
                `).join('');

                // إضافة مستمعي الأحداث لأزرار التعديل
                document.querySelectorAll('.edit-prayer-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        const currentPrayer = btn.dataset.prayer;
                        const currentTime = document.getElementById(`${currentPrayer}-time-display`).textContent;
                        const modal = document.getElementById('edit-prayer-modal');
                        const overlay = document.getElementById('prayer-modal-overlay');
                        const timeInput = document.getElementById('edit-prayer-time');
                        const saveBtn = document.getElementById('save-prayer-time');

                        timeInput.value = currentTime;
                        modal.classList.add('active');
                        overlay.classList.add('active');

                        // حفظ التعديل
                        saveBtn.onclick = () => {
                            if (timeInput.value) {
                                const savedTimes = JSON.parse(localStorage.getItem('manualPrayerTimes') || '{}');
                                savedTimes[currentPrayer] = timeInput.value;
                                localStorage.setItem('manualPrayerTimes', JSON.stringify(savedTimes));

                                // تحديث العرض في جميع الأماكن
                                document.getElementById(`${currentPrayer}-time-display`).textContent = timeInput.value;
                                const horizontalElement = document.querySelector(`.prayer-time[data-prayer="${currentPrayer}"]`);
                                if (horizontalElement) {
                                    horizontalElement.textContent = timeInput.value;
                                }

                                // تحديث كائن prayerTimes
                                if (!window.prayerTimes) window.prayerTimes = {};
                                if (!window.prayerTimes[currentCity]) window.prayerTimes[currentCity] = {};
                                window.prayerTimes[currentCity][currentPrayer] = timeInput.value;

                                // تحديث قائمة الصلوات القادمة
                                updateUpcomingPrayers();

                                // إغلاق النافذة
                                modal.classList.remove('active');
                                overlay.classList.remove('active');
                            }
                        };
                    });
                });
            }

            // تحديث العد التنازلي
            updateCountdown();
        }

        // دالة حفظ إعدادات الأذان
        function saveAdhanSettings() {
            const enableAdhan = document.getElementById('enable-adhan').checked;
            const adhanVolume = document.getElementById('adhan-volume').value;

            localStorage.setItem('adhanSettings', JSON.stringify({
                enabled: enableAdhan,
                volume: adhanVolume
            }));

            alert('تم حفظ إعدادات الأذان بنجاح');
        }

        // إضافة مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', () => {
            loadSavedPrayerTimes();

            // إضافة عنصر عرض اسم المسجد
            const mosqueNameDisplay = document.createElement('div');
            mosqueNameDisplay.className = 'mosque-name-display';
            document.body.appendChild(mosqueNameDisplay);

            // تحميل إعدادات المسجد المحفوظة
            const savedMosqueName = localStorage.getItem('mosqueName') || 'مسجد';
            const savedFontSize = localStorage.getItem('mosqueFontSize') || '24';
            const savedColor = localStorage.getItem('mosqueColor') || '#D4AF37';

            document.getElementById('mosque-name-input').value = savedMosqueName;
            document.getElementById('mosque-font-size').textContent = savedFontSize;
            document.getElementById('mosque-text-color').value = savedColor;

            // تحديث العرض
            updateMosqueDisplay();

            // إضافة مستمعي الأحداث
            document.getElementById('mosque-name-input').addEventListener('input', function() {
                localStorage.setItem('mosqueName', this.value);
                updateMosqueDisplay();
            });

            document.getElementById('increase-mosque-text').addEventListener('click', function() {
                let size = parseInt(document.getElementById('mosque-font-size').textContent);
                if (size < 72) {
                    size += 2;
                    document.getElementById('mosque-font-size').textContent = size;
                    localStorage.setItem('mosqueFontSize', size);
                    updateMosqueDisplay();
                }
            });

            document.getElementById('decrease-mosque-text').addEventListener('click', function() {
                let size = parseInt(document.getElementById('mosque-font-size').textContent);
                if (size > 16) {
                    size -= 2;
                    document.getElementById('mosque-font-size').textContent = size;
                    localStorage.setItem('mosqueFontSize', size);
                    updateMosqueDisplay();
                }
            });

            document.getElementById('mosque-text-color').addEventListener('change', function() {
                localStorage.setItem('mosqueColor', this.value);
                updateMosqueDisplay();
            });

            function updateMosqueDisplay() {
                const mosqueNameDisplay = document.querySelector('.mosque-name-display');
                if (!mosqueNameDisplay) return;

                const mosqueName = document.getElementById('mosque-name-input')?.value || 'مسجد';
                const size = document.getElementById('mosque-font-size')?.textContent || '24';
                const color = document.getElementById('mosque-text-color')?.value || '#D4AF37';

                mosqueNameDisplay.style.fontSize = `${size}px`;
                mosqueNameDisplay.style.color = color;
                mosqueNameDisplay.textContent = mosqueName;

                // Save to localStorage
                localStorage.setItem('mosqueName', mosqueName);
                localStorage.setItem('mosqueFontSize', size);
                localStorage.setItem('mosqueColor', color);
            }

            // تحميل إعدادات الأذان المحفوظة
            const savedAdhanSettings = JSON.parse(localStorage.getItem('adhanSettings') || '{}');
            document.getElementById('enable-adhan').checked = savedAdhanSettings.enabled ?? true;
            document.getElementById('adhan-volume').value = savedAdhanSettings.volume ?? 1;

            // إضافة مستمع حدث لزر حفظ إعدادات الأذان
            const saveAdhanButton = document.getElementById('save-adhan-settings');
            if (saveAdhanButton) {
                saveAdhanButton.addEventListener('click', saveAdhanSettings);
            }

            // إضافة مستمع حدث لزر إغلاق النافذة المنبثقة
            const closeModalButton = document.getElementById('close-edit-prayer-modal');
            if (closeModalButton) {
                closeModalButton.addEventListener('click', closeModal);
            }

            // حفظ إعدادات الأذان عند التغيير
            const enableAdhanInput = document.getElementById('enable-adhan');
            const adhanVolumeInput = document.getElementById('adhan-volume');
            const resetPrayerTimesBtn = document.getElementById('reset-prayer-times');
            const syncPrayerTimesBtn = document.getElementById('sync-prayer-times');

            if (enableAdhanInput) {
                enableAdhanInput.addEventListener('change', saveAdhanSettings);
            }

            if (adhanVolumeInput) {
                adhanVolumeInput.addEventListener('input', saveAdhanSettings);
            }

            // فحص فوري لوقت الصلاة
            checkPrayerTimeAndPlayAdhan();

            document.querySelectorAll('.save-time-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const prayer = btn.dataset.prayer;
                    savePrayerTime(prayer);
                });
            });

            if (resetPrayerTimesBtn) {
                resetPrayerTimesBtn.addEventListener('click', resetPrayerTimes);
            }

            if (syncPrayerTimesBtn) {
                syncPrayerTimesBtn.addEventListener('click', syncPrayerTimes);
            }
        });

        // إضافة مستمعي الأحداث لأزرار التعديل
        document.addEventListener('DOMContentLoaded', () => {
            const modal = document.getElementById('edit-prayer-modal');
            const overlay = document.getElementById('prayer-modal-overlay');
            const timeInput = document.getElementById('edit-prayer-time');
            const saveBtn = document.getElementById('save-prayer-time');
            const cancelBtn = document.getElementById('cancel-prayer-time');
            let currentPrayer = null;

            // فتح نافذة التعديل
            if (modal && overlay && timeInput) {
                document.querySelectorAll('.edit-prayer-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        currentPrayer = btn.dataset.prayer;
                        const timeDisplay = document.getElementById(`${currentPrayer}-time-display`);
                        if (timeDisplay) {
                            timeInput.value = timeDisplay.textContent;
                            modal.classList.add('active');
                            overlay.classList.add('active');
                        }
                    });
                });

                // حفظ التعديل
                if (saveBtn) {
                    saveBtn.addEventListener('click', () => {
                        if (currentPrayer && timeInput.value) {
                            const savedTimes = JSON.parse(localStorage.getItem('manualPrayerTimes') || '{}');
                            savedTimes[currentPrayer] = timeInput.value;
                            localStorage.setItem('manualPrayerTimes', JSON.stringify(savedTimes));

                            // تحديث العرض
                            const timeDisplay = document.getElementById(`${currentPrayer}-time-display`);
                            if (timeDisplay) {
                                timeDisplay.textContent = timeInput.value;
                            }

                            // تحديث كائن prayerTimes
                            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                            if (!window.prayerTimes) window.prayerTimes = {};
                            if (!window.prayerTimes[currentCity]) window.prayerTimes[currentCity] = {};
                            window.prayerTimes[currentCity][currentPrayer] = timeInput.value;

                            // إغلاق النافذة
                            closeModal();

                            // تحديث العرض
                            updatePrayerTimes();
                        }
                    });
                }

                // إلغاء التعديل
                if (cancelBtn) {
                    cancelBtn.addEventListener('click', closeModal);
                }
            }
        });

        // تحديث الدولة
        function updateCountry() {
            const countrySelect = document.getElementById('country-select');
            if (!countrySelect) {
                console.warn('عنصر قائمة الدول غير موجود');
                return;
            }

            const country = countrySelect.value;
            const citySelect = document.getElementById('city-select');

            if (!citySelect) {
                console.warn('عنصر قائمة المدن غير موجود');
                return;
            }

            // مسح المدن السابقة
            citySelect.innerHTML = '';

            // التحقق من وجود الدولة في قاعدة البيانات
            if (!cityCoordinates[country]) {
                console.warn(`الدولة ${country} غير موجودة في قاعدة البيانات`);
                return;
            }

            // إضافة مدن الدولة المحددة
            const cities = Object.keys(cityCoordinates[country]);
            cities.forEach(city => {
                const option = document.createElement('option');
                option.value = city;
                option.textContent = city;
                citySelect.appendChild(option);
            });

            // تحديث الموقع
            if (citySelect.value) {
                updateLocation(country, citySelect.value);
            }
        }

        // تحديث المدينة
        function updateCity() {
            const country = document.getElementById('country-select').value;
            const city = document.getElementById('city-select').value;
            updateLocation(country, city);
        }

        // تحديث طريقة الحساب
        function updateMethod() {
            const method = document.getElementById('method-select').value;
            updateCalculationMethod(method);
        }

        // تحديث المذهب
        function updateMadhab() {
            const madhab = document.getElementById('madhab-select').value;
            updateMadhab(madhab);
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', () => {
            console.log('بدء تهيئة التطبيق...');

            // تحميل مدة الإقامة المحفوظة
            if (typeof loadIqamaDurations === 'function') {
                console.log('تحميل مدة الإقامة المحفوظة...');
                loadIqamaDurations();
            } else {
                console.warn('دالة loadIqamaDurations غير متوفرة');
            }

            // إضافة مستمع الحدث لزر تحديث مواقيت الصلاة
            const mainUpdatePrayerTimesBtn = document.getElementById('main-update-prayer-times');
            if (mainUpdatePrayerTimesBtn) {
                mainUpdatePrayerTimesBtn.addEventListener('click', function() {
                    // عرض رسالة تأكيد
                    if (confirm('هل تريد تحديث مواقيت الصلاة؟')) {
                        // عرض رسالة تحميل
                        showNotification('جاري تحديث مواقيت الصلاة...', 'info');

                        // تحديث مواقيت الصلاة
                        setTimeout(function() {
                            forceUpdatePrayerTimes();
                        }, 500);
                    }
                });
            }

            // إضافة مستمع حدث لزر حفظ مدة الإقامة
            const saveIqamaBtn = document.getElementById('save-iqamah-times');
            if (saveIqamaBtn) {
                console.log('إضافة مستمع حدث لزر حفظ مدة الإقامة...');
                saveIqamaBtn.addEventListener('click', function() {
                    if (typeof saveIqamaDurations === 'function') {
                        saveIqamaDurations();
                    } else {
                        console.error('دالة saveIqamaDurations غير متوفرة');
                        alert('حدث خطأ: دالة حفظ مدة الإقامة غير متوفرة');
                    }
                });
            } else {
                console.warn('زر حفظ مدة الإقامة غير موجود');
            }

            // إضافة مستمع حدث للتحديث اليومي
            window.addEventListener('prayerTimesUpdated', function() {
                console.log('تم استلام حدث تحديث مواقيت الصلاة اليومي');

                // تحديث عرض مواقيت الصلاة
                if (typeof updateAllPrayerTimesDisplays === 'function') {
                    updateAllPrayerTimesDisplays();
                    console.log('تم تحديث عرض مواقيت الصلاة بعد التحديث اليومي');
                } else {
                    console.warn('دالة updateAllPrayerTimesDisplays غير متوفرة');

                    // محاولة استخدام دالة بديلة
                    if (typeof updatePrayerTimes === 'function') {
                        updatePrayerTimes();
                        console.log('تم تحديث عرض مواقيت الصلاة باستخدام دالة بديلة');
                    }
                }

                // عرض إشعار للمستخدم
                if (typeof showNotification === 'function') {
                    showNotification('تم تحديث مواقيت الصلاة تلقائيًا', 'success');
                }
            });
            // تحديث قائمة المدن عند تغيير الدولة
            const countrySelectElement = document.getElementById('country-select');
            if (countrySelectElement) {
                countrySelectElement.addEventListener('change', updateCountry);
            }

            // تحديث المواقيت عند تغيير المدينة
            const citySelectElement = document.getElementById('city-select');
            if (citySelectElement) {
                citySelectElement.addEventListener('change', updateCity);
            }

            // تحديث المواقيت عند تغيير طريقة الحساب
            const methodSelectElement = document.getElementById('method-select');
            if (methodSelectElement) {
                methodSelectElement.addEventListener('change', updateMethod);
            }

            // تحديث المواقيت عند تغيير المذهب
            const madhabSelectElement = document.getElementById('madhab-select');
            if (madhabSelectElement) {
                madhabSelectElement.addEventListener('change', updateMadhab);
            }
        });

        // دالة لتحميل التعديلات اليدوية المحفوظة
        function loadManualAdjustments() {
            try {
                const savedAdjustments = JSON.parse(localStorage.getItem('manual_adjustments')) || {};

                // تطبيق التعديلات المحفوظة على حقول الإدخال
                Object.keys(savedAdjustments).forEach(prayer => {
                    const input = document.getElementById(`${prayer}-adjustment`);
                    if (input) {
                        input.value = savedAdjustments[prayer];
                    }
                });

                console.log('تم تحميل التعديلات اليدوية بنجاح');
            } catch (error) {
                console.error('خطأ في تحميل التعديلات اليدوية:', error);
            }
        }

        // تحميل التعديلات المحفوظة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            try {
                loadManualAdjustments();
            } catch (error) {
                console.error('خطأ في تحميل التعديلات اليدوية:', error);
            }
        });

        // دالة لحفظ مدة التعتيم
        function saveDarknessDurations() {
            const durations = {
                fajr: parseInt(document.getElementById('fajr-darkness-duration').value) || 15,
                dhuhr: parseInt(document.getElementById('dhuhr-darkness-duration').value) || 15,
                asr: parseInt(document.getElementById('asr-darkness-duration').value) || 15,
                maghrib: parseInt(document.getElementById('maghrib-darkness-duration').value) || 15,
                isha: parseInt(document.getElementById('isha-darkness-duration').value) || 15
            };

            localStorage.setItem('darkness_durations', JSON.stringify(durations));
            alert('تم حفظ مدة التعتيم بنجاح');
        }

        // تحميل مدة التعتيم المحفوظة
        function loadDarknessDurations() {
            const savedDurations = JSON.parse(localStorage.getItem('darkness_durations')) || {};

            Object.keys(savedDurations).forEach(prayer => {
                const input = document.getElementById(`${prayer}-darkness-duration`);
                if (input) {
                    input.value = savedDurations[prayer];
                }
            });
        }

        // تحميل الإعدادات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            loadDarknessDurations();
        });

        // حذف العناصر المكررة
        document.addEventListener('DOMContentLoaded', function() {
            // حذف الأقسام المكررة لمدة التعتيم
            const darknessSections = document.querySelectorAll('.darkness-settings, .darkness-duration-section');
            if (darknessSections.length > 1) {
                for (let i = 1; i < darknessSections.length; i++) {
                    darknessSections[i].remove();
                }
            }

            // حذف الأقسام المكررة لتعديل المواقيت
            const adjustmentSections = document.querySelectorAll('.manual-adjustment-section, .prayer-times-settings');
            if (adjustmentSections.length > 1) {
                for (let i = 1; i < adjustmentSections.length; i++) {
                    adjustmentSections[i].remove();
                }
            }

            // حذف الأقسام المكررة للتعديل اليدوي للمواقيت
            const allSettingsSections = document.querySelectorAll('.settings-section');
            const manualPrayerTimesSections = [];

            // البحث عن أقسام التعديل اليدوي للمواقيت
            allSettingsSections.forEach(section => {
                if (section.querySelector('.manual-prayer-times')) {
                    manualPrayerTimesSections.push(section);
                }
            });

            if (manualPrayerTimesSections.length > 1) {
                console.log(`تم العثور على ${manualPrayerTimesSections.length} قسم مكرر للتعديل اليدوي للمواقيت`);
                for (let i = 1; i < manualPrayerTimesSections.length; i++) {
                    manualPrayerTimesSections[i].remove();
                    console.log(`تم حذف القسم المكرر رقم ${i} للتعديل اليدوي للمواقيت`);
                }
            }
        });

        // إضافة إحداثيات المدن الرئيسية
        const cityCoordinates = {
            "الأردن": {
                "عمان": { lat: 31.9539, lng: 35.9106 },
                "الزرقاء": { lat: 32.0725, lng: 36.0841 },
                "إربد": { lat: 32.5556, lng: 35.8500 },
                "العقبة": { lat: 29.5328, lng: 35.0084 }
            },
            "السعودية": {
                "الرياض": { lat: 24.7136, lng: 46.6753 },
                "جدة": { lat: 21.2854, lng: 39.2376 },
                "مكة": { lat: 21.3891, lng: 39.8579 },
                "المدينة": { lat: 24.4539, lng: 39.6066 }
            },
            "مصر": {
                "القاهرة": { lat: 30.0444, lng: 31.2357 },
                "الإسكندرية": { lat: 31.2001, lng: 29.9187 }
            },
            "الإمارات": {
                "دبي": { lat: 25.2048, lng: 55.2708 },
                "أبوظبي": { lat: 24.4539, lng: 54.3773 }
            },
            "قطر": {
                "الدوحة": { lat: 25.2867, lng: 51.5333 }
            },
            "الكويت": {
                "مدينة الكويت": { lat: 29.3759, lng: 47.9774 }
            },
            "عُمان": {
                "مسقط": { lat: 23.5859, lng: 58.4059 }
            },
            "البحرين": {
                "المنامة": { lat: 26.2285, lng: 50.5860 }
            },
            "لبنان": {
                "بيروت": { lat: 33.8938, lng: 35.5018 }
            },
            "فلسطين": {
                "القدس": { lat: 31.7683, lng: 35.2137 }
            },
            "العراق": {
                "بغداد": { lat: 33.3152, lng: 44.3661 }
            },
            "سوريا": {
                "دمشق": { lat: 33.5138, lng: 36.2765 }
            },
            "تركيا": {
                "إسطنبول": { lat: 41.0082, lng: 28.9784 }
            }
        };

        // تحديث قائمة المدن عند اختيار الدولة
        function updateCountry() {
            const countrySelect = document.getElementById('country-select');
            const citySelect = document.getElementById('city-select');
            const selectedCountry = countrySelect.value;

            // مسح قائمة المدن الحالية
            citySelect.innerHTML = '<option value="">اختر المدينة</option>';

            // إضافة المدن المتاحة للدولة المختارة
            if (selectedCountry && cityCoordinates[selectedCountry]) {
                Object.keys(cityCoordinates[selectedCountry]).forEach(city => {
                    const option = document.createElement('option');
                    option.value = city;
                    option.textContent = city;
                    citySelect.appendChild(option);
                });
            }

            // تحديث مواقيت الصلاة عند تغيير الدولة
            if (selectedCountry && citySelect.value) {
                updateLocation(selectedCountry, citySelect.value);
            }
        }

        // تحديث الموقع
        function updateLocation(country, city) {
            if (cityCoordinates[country] && cityCoordinates[country][city]) {
                currentLocation = cityCoordinates[country][city];
                updatePrayerTimes();
            }
        }

        // إضافة مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            const countrySelect = document.getElementById('country-select');
            const citySelect = document.getElementById('city-select');

            if (countrySelect) {
                countrySelect.addEventListener('change', updateCountry);
            }

            if (citySelect) {
                citySelect.addEventListener('change', function() {
                    const country = document.getElementById('country-select').value;
                    updateLocation(country, this.value);
                });
            }

            // تحميل مدة الإقامة المحفوظة
            loadIqamaDurations();

            // إضافة مستمع حدث لزر حفظ مدة الإقامة
            const saveIqamaBtn = document.getElementById('save-iqamah-times');
            if (saveIqamaBtn) {
                saveIqamaBtn.addEventListener('click', function() {
                    saveIqamaDurations();
                });
            }
        });

        // إضافة دالة لحفظ مدة الإقامة
        function saveIqamaDurations() {
            try {
                console.log('بدء حفظ مدة الإقامة...');

                // قراءة القيم من حقول الإدخال
                const durations = {
                    fajr: parseInt(document.getElementById('fajr-iqama-duration').value) || 15,
                    dhuhr: parseInt(document.getElementById('dhuhr-iqama-duration').value) || 15,
                    asr: parseInt(document.getElementById('asr-iqama-duration').value) || 15,
                    maghrib: parseInt(document.getElementById('maghrib-iqama-duration').value) || 10,
                    isha: parseInt(document.getElementById('isha-iqama-duration').value) || 15
                };

                console.log('مدة الإقامة المقروءة من الحقول:', durations);

                // حفظ المدد في التخزين المحلي
                localStorage.setItem('iqama_durations', JSON.stringify(durations));

                // حفظ المدد في التخزين المحلي بتنسيق آخر للتوافق مع الأنظمة الأخرى
                localStorage.setItem('iqamahTimes', JSON.stringify(durations));

                // تحديث المتغير العالمي
                window.iqamahTimes = durations;

                // تحديث المتغير العالمي الآخر للتوافق
                window.iqamaTimes = durations;

                console.log('تم حفظ مدة الإقامة بنجاح:', durations);

                // إظهار إشعار نجاح
                if (typeof showNotification === 'function') {
                    showNotification('تم حفظ مدة الإقامة بنجاح', 'success');
                } else {
                    alert('تم حفظ مدة الإقامة بنجاح');
                }

                return true;
            } catch (error) {
                console.error('خطأ في حفظ مدة الإقامة:', error);

                // إظهار إشعار خطأ
                if (typeof showNotification === 'function') {
                    showNotification('حدث خطأ أثناء حفظ مدة الإقامة', 'error');
                } else {
                    alert('حدث خطأ أثناء حفظ مدة الإقامة');
                }

                return false;
            }
        }

        // إضافة دالة لتحميل مدة الإقامة المحفوظة
        function loadIqamaDurations() {
            try {
                console.log('بدء تحميل مدة الإقامة المحفوظة...');

                // محاولة تحميل المدد من التخزين المحلي بالتنسيق الأول
                let savedDurations = {};
                try {
                    savedDurations = JSON.parse(localStorage.getItem('iqama_durations')) || {};
                    console.log('تم تحميل مدة الإقامة من التخزين المحلي (التنسيق الأول):', savedDurations);
                } catch (error) {
                    console.warn('خطأ في تحميل مدة الإقامة من التخزين المحلي (التنسيق الأول):', error);
                }

                // إذا لم يتم العثور على مدد محفوظة بالتنسيق الأول، حاول التنسيق الثاني
                if (Object.keys(savedDurations).length === 0) {
                    try {
                        savedDurations = JSON.parse(localStorage.getItem('iqamahTimes')) || {};
                        console.log('تم تحميل مدة الإقامة من التخزين المحلي (التنسيق الثاني):', savedDurations);
                    } catch (error) {
                        console.warn('خطأ في تحميل مدة الإقامة من التخزين المحلي (التنسيق الثاني):', error);
                    }
                }

                // إذا لم يتم العثور على مدد محفوظة، استخدم القيم الافتراضية
                if (Object.keys(savedDurations).length === 0) {
                    savedDurations = {
                        fajr: 15,
                        dhuhr: 15,
                        asr: 15,
                        maghrib: 10,
                        isha: 15
                    };
                    console.log('استخدام القيم الافتراضية لمدة الإقامة:', savedDurations);
                }

                // تطبيق المدد المحفوظة على حقول الإدخال
                Object.keys(savedDurations).forEach(prayer => {
                    const input = document.getElementById(`${prayer}-iqama-duration`);
                    if (input) {
                        input.value = savedDurations[prayer];
                    }
                });

                // تحديث المتغيرات العالمية
                window.iqamahTimes = savedDurations;
                window.iqamaTimes = savedDurations;

                console.log('تم تحميل وتطبيق مدة الإقامة المحفوظة بنجاح');

                return true;
            } catch (error) {
                console.error('خطأ في تحميل مدة الإقامة المحفوظة:', error);

                // استخدام القيم الافتراضية في حالة الخطأ
                const defaultDurations = {
                    fajr: 15,
                    dhuhr: 15,
                    asr: 15,
                    maghrib: 10,
                    isha: 15
                };

                // تطبيق القيم الافتراضية على حقول الإدخال
                Object.keys(defaultDurations).forEach(prayer => {
                    const input = document.getElementById(`${prayer}-iqama-duration`);
                    if (input) {
                        input.value = defaultDurations[prayer];
                    }
                });

                // تحديث المتغيرات العالمية
                window.iqamahTimes = defaultDurations;
                window.iqamaTimes = defaultDurations;

                console.log('تم استخدام القيم الافتراضية لمدة الإقامة بسبب الخطأ');

                return false;
            }
        }

        // دالة لتحميل المواقيت المعدلة يدوياً من التخزين المحلي
        function loadManualPrayerTimes() {
            try {
                console.log('بدء تحميل المواقيت المعدلة يدوياً...');

                // تهيئة المتغير ككائن فارغ إذا لم يكن موجوداً
                if (!manualPrayerTimes) {
                    manualPrayerTimes = {};
                }

                // تحميل حالة تفعيل المواقيت المعدلة يدوياً
                manualPrayerTimesEnabled = localStorage.getItem('manualPrayerTimesEnabled') === 'true';
                console.log('حالة المواقيت المعدلة يدوياً:', manualPrayerTimesEnabled ? 'مفعلة' : 'غير مفعلة');

                // تحميل المواقيت المعدلة يدوياً
                const savedManualTimes = localStorage.getItem('manualPrayerTimes');
                if (savedManualTimes) {
                    manualPrayerTimes = JSON.parse(savedManualTimes);
                    console.log('تم تحميل المواقيت المعدلة يدوياً:', manualPrayerTimes);

                    // الحصول على المدينة الحالية
                    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

                    // التحقق من وجود مواقيت معدلة للمدينة الحالية
                    if (manualPrayerTimes[currentCity]) {
                        console.log('تم العثور على مواقيت معدلة يدوياً للمدينة الحالية:', currentCity);

                        // تحديث المواقيت العالمية بالمواقيت المعدلة يدوياً
                        if (!window.prayerTimes) {
                            window.prayerTimes = {};
                        }

                        // نسخ المواقيت المعدلة يدوياً
                        const manualTimes = JSON.parse(JSON.stringify(manualPrayerTimes[currentCity]));

                        // تحديث المواقيت العالمية للمدينة الحالية
                        window.prayerTimes[currentCity] = manualTimes;

                        // تحديث المواقيت الثابتة أيضاً
                        if (!window.AMMAN_PRAYER_TIMES) {
                            window.AMMAN_PRAYER_TIMES = {};
                        }
                        window.AMMAN_PRAYER_TIMES[currentCity] = manualTimes;

                        console.log('تم تحديث المواقيت العالمية بالمواقيت المعدلة يدوياً');
                    } else {
                        console.log('لا توجد مواقيت معدلة يدوياً للمدينة الحالية:', currentCity);
                    }

                    return true;
                } else {
                    console.log('لم يتم العثور على مواقيت معدلة يدوياً محفوظة');
                }
            } catch (error) {
                console.error('خطأ في تحميل المواقيت المعدلة يدوياً:', error);
                // إعادة تهيئة المتغير في حالة حدوث خطأ
                manualPrayerTimes = {};
                manualPrayerTimesEnabled = false;
            }
            return false;
        }

        // دالة لحفظ المواقيت المعدلة يدوياً في التخزين المحلي
        function saveManualPrayerTimes() {
            try {
                console.log('بدء حفظ المواقيت المعدلة يدوياً...');

                // التحقق من وجود المتغير
                if (!manualPrayerTimes) {
                    console.warn('متغير المواقيت المعدلة يدوياً غير موجود، سيتم تهيئته');
                    manualPrayerTimes = {};
                }

                // التحقق من صحة البيانات قبل الحفظ
                if (typeof manualPrayerTimes !== 'object') {
                    console.error('متغير المواقيت المعدلة يدوياً ليس كائناً صالحاً');
                    manualPrayerTimes = {};
                }

                // حفظ المواقيت المعدلة في التخزين المحلي
                const jsonString = JSON.stringify(manualPrayerTimes);
                localStorage.setItem('manualPrayerTimes', jsonString);
                localStorage.setItem('manualPrayerTimesEnabled', manualPrayerTimesEnabled.toString());

                console.log('تم حفظ المواقيت المعدلة يدوياً بنجاح');
                console.log('حجم البيانات المحفوظة:', jsonString.length, 'بايت');
                console.log('حالة المواقيت المعدلة يدوياً:', manualPrayerTimesEnabled ? 'مفعلة' : 'غير مفعلة');

                // عرض ملخص للمدن المحفوظة
                const cities = Object.keys(manualPrayerTimes);
                console.log('المدن المحفوظة:', cities.length > 0 ? cities.join(', ') : 'لا توجد مدن محفوظة');

                return true;
            } catch (error) {
                console.error('خطأ في حفظ المواقيت المعدلة يدوياً:', error);

                // محاولة حفظ البيانات بطريقة أخرى في حالة الخطأ
                try {
                    console.warn('محاولة حفظ البيانات بطريقة بديلة...');

                    // الحصول على المدينة الحالية
                    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

                    // حفظ المواقيت المعدلة للمدينة الحالية فقط
                    if (manualPrayerTimes && manualPrayerTimes[currentCity]) {
                        const singleCityData = {
                            [currentCity]: manualPrayerTimes[currentCity]
                        };

                        localStorage.setItem('manualPrayerTimes', JSON.stringify(singleCityData));
                        localStorage.setItem('manualPrayerTimesEnabled', manualPrayerTimesEnabled.toString());

                        console.log('تم حفظ المواقيت المعدلة للمدينة الحالية فقط:', currentCity);
                        return true;
                    }
                } catch (fallbackError) {
                    console.error('فشلت محاولة الحفظ البديلة:', fallbackError);
                }

                return false;
            }
        }

        // دالة لتحديث حقول التعديل اليدوي بالمواقيت الحالية
        function updateManualPrayerTimeInputs() {
            try {
                // التحقق من وجود حقول الإدخال
                const fajrInput = document.getElementById('manual-fajr');
                const sunriseInput = document.getElementById('manual-sunrise');
                const dhuhrInput = document.getElementById('manual-dhuhr');
                const asrInput = document.getElementById('manual-asr');
                const maghribInput = document.getElementById('manual-maghrib');
                const ishaInput = document.getElementById('manual-isha');

                if (!fajrInput || !sunriseInput || !dhuhrInput || !asrInput || !maghribInput || !ishaInput) {
                    console.error('لم يتم العثور على حقول الإدخال للتعديل اليدوي');
                    return;
                }

                const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                let times;

                // استخدام المواقيت المعدلة يدوياً إذا كانت مفعلة
                if (manualPrayerTimesEnabled && manualPrayerTimes && manualPrayerTimes[currentCity]) {
                    times = manualPrayerTimes[currentCity];
                    console.log('استخدام المواقيت المعدلة يدوياً لتحديث حقول الإدخال:', times);
                } else if (window.prayerTimes && window.prayerTimes[currentCity]) {
                    // استخدام المواقيت المحسوبة
                    times = window.prayerTimes[currentCity];
                    console.log('استخدام المواقيت المحسوبة لتحديث حقول الإدخال:', times);
                } else if (window.AMMAN_PRAYER_TIMES && window.AMMAN_PRAYER_TIMES[currentCity]) {
                    // استخدام المواقيت الثابتة
                    times = window.AMMAN_PRAYER_TIMES[currentCity];
                    console.log('استخدام المواقيت الثابتة لتحديث حقول الإدخال:', times);
                } else {
                    console.error('لا توجد مواقيت متاحة لتحديث حقول التعديل اليدوي');
                    return;
                }

                // التحقق من صحة المواقيت
                if (!times || typeof times !== 'object') {
                    console.error('المواقيت غير صالحة:', times);
                    return;
                }

                // تحديث حقول التعديل اليدوي
                if (times.fajr) fajrInput.value = times.fajr;
                if (times.sunrise) sunriseInput.value = times.sunrise;
                if (times.dhuhr) dhuhrInput.value = times.dhuhr;
                if (times.asr) asrInput.value = times.asr;
                if (times.maghrib) maghribInput.value = times.maghrib;
                if (times.isha) ishaInput.value = times.isha;

                console.log('تم تحديث حقول التعديل اليدوي بالمواقيت الحالية');
            } catch (error) {
                console.error('خطأ في تحديث حقول التعديل اليدوي:', error);
            }
        }

        // دالة لتطبيق المواقيت المعدلة يدوياً
        function applyManualPrayerTimes() {
            try {
                console.log('بدء تطبيق المواقيت المعدلة يدوياً...');

                // التحقق من تفعيل المواقيت المعدلة يدوياً
                if (!manualPrayerTimesEnabled) {
                    console.log('المواقيت المعدلة يدوياً غير مفعلة');
                    return false;
                }

                // تهيئة المتغير إذا لم يكن موجوداً
                if (!manualPrayerTimes) {
                    console.error('متغير المواقيت المعدلة يدوياً غير موجود');
                    manualPrayerTimes = {};
                    return false;
                }

                // الحصول على المدينة الحالية
                const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                console.log('المدينة الحالية:', currentCity);

                // التحقق من وجود مواقيت معدلة للمدينة الحالية
                if (!manualPrayerTimes[currentCity]) {
                    console.log('لا توجد مواقيت معدلة يدوياً للمدينة الحالية:', currentCity);
                    return false;
                }

                console.log('المواقيت المعدلة يدوياً للمدينة الحالية:', manualPrayerTimes[currentCity]);

                // التحقق من صحة المواقيت المعدلة
                const requiredTimes = ['fajr', 'sunrise', 'dhuhr', 'asr', 'maghrib', 'isha'];
                const missingTimes = [];

                for (const time of requiredTimes) {
                    if (!manualPrayerTimes[currentCity][time]) {
                        console.error(`وقت ${time} غير موجود في المواقيت المعدلة يدوياً`);
                        missingTimes.push(time);
                    }
                }

                // إذا كانت هناك أوقات مفقودة، حاول استكمالها من المواقيت المحسوبة أو الثابتة
                if (missingTimes.length > 0) {
                    console.warn(`هناك أوقات مفقودة في المواقيت المعدلة يدوياً: ${missingTimes.join(', ')}`);

                    // محاولة استكمال الأوقات المفقودة من المواقيت المحسوبة أو الثابتة
                    const calculatedTimes = window.prayerTimes?.[currentCity] || window.AMMAN_PRAYER_TIMES?.[currentCity] || {};

                    for (const time of missingTimes) {
                        if (calculatedTimes[time]) {
                            console.log(`استكمال وقت ${time} من المواقيت المحسوبة: ${calculatedTimes[time]}`);
                            manualPrayerTimes[currentCity][time] = calculatedTimes[time];
                        } else {
                            // إنشاء وقت افتراضي
                            const defaultTimes = {
                                fajr: '04:00',
                                sunrise: '05:30',
                                dhuhr: '12:00',
                                asr: '15:30',
                                maghrib: '18:30',
                                isha: '20:00'
                            };

                            console.warn(`استكمال وقت ${time} بقيمة افتراضية: ${defaultTimes[time]}`);
                            manualPrayerTimes[currentCity][time] = defaultTimes[time];
                        }
                    }

                    // حفظ التغييرات
                    saveManualPrayerTimes();
                }

                // نسخ المواقيت المعدلة يدوياً إلى متغير مؤقت
                const manualTimes = JSON.parse(JSON.stringify(manualPrayerTimes[currentCity]));
                console.log('نسخة من المواقيت المعدلة يدوياً:', manualTimes);

                // تحديث المواقيت العالمية بالمواقيت المعدلة يدوياً
                if (!window.prayerTimes) {
                    window.prayerTimes = {};
                }

                // تحديث المواقيت العالمية للمدينة الحالية فقط
                window.prayerTimes[currentCity] = manualTimes;
                console.log('تم تحديث المواقيت العالمية للمدينة الحالية:', window.prayerTimes[currentCity]);

                // تحديث المواقيت الثابتة أيضاً للمدينة الحالية فقط
                if (!window.AMMAN_PRAYER_TIMES) {
                    window.AMMAN_PRAYER_TIMES = {};
                }

                window.AMMAN_PRAYER_TIMES[currentCity] = manualTimes;
                console.log('تم تحديث المواقيت الثابتة للمدينة الحالية:', window.AMMAN_PRAYER_TIMES[currentCity]);

                // تحديث العرض
                if (typeof updatePrayerTimesDisplay === 'function') {
                    updatePrayerTimesDisplay(manualTimes, '24');
                    console.log('تم تحديث عرض المواقيت');
                }

                // تحديث حقول التعديل اليدوي
                if (typeof updateManualPrayerTimeInputs === 'function') {
                    updateManualPrayerTimeInputs();
                    console.log('تم تحديث حقول التعديل اليدوي');
                }

                // تحديث العد التنازلي والصلاة القادمة
                if (typeof displayRemainingPrayerTimes === 'function') {
                    displayRemainingPrayerTimes();
                    console.log('تم تحديث العد التنازلي للصلاة القادمة');
                }

                // تحديث نص الصلاة القادمة
                if (typeof updateNextPrayerText === 'function') {
                    updateNextPrayerText();
                    console.log('تم تحديث نص الصلاة القادمة');
                }

                // تحديث العد التنازلي
                if (typeof updateCountdown === 'function') {
                    updateCountdown();
                    console.log('تم تحديث العد التنازلي');
                }

                console.log('تم تطبيق المواقيت المعدلة يدوياً للمدينة بنجاح:', currentCity);
                return true;
            } catch (error) {
                console.error('خطأ في تطبيق المواقيت المعدلة يدوياً:', error);
                return false;
            }
        }

        // دالة للتحديث التلقائي للمواقيت مع الحفاظ على التعديلات اليدوية
        function setupAutoUpdate() {
            console.log('بدء إعداد التحديث التلقائي للمواقيت...');

            // إلغاء المؤقت السابق إذا كان موجوداً
            if (autoUpdateInterval) {
                clearInterval(autoUpdateInterval);
                console.log('تم إلغاء المؤقت السابق للتحديث التلقائي');
            }

            // التحديث الأولي عند بدء التشغيل
            try {
                console.log('تنفيذ التحديث الأولي للمواقيت...');

                // تحديث المواقيت المحسوبة مع الحفاظ على التعديلات اليدوية
                updateCalculatedTimesWithManualAdjustments();

                console.log('تم تنفيذ التحديث الأولي للمواقيت بنجاح');
            } catch (error) {
                console.error('خطأ في التحديث الأولي للمواقيت:', error);
            }

            // إعداد مؤقت للتحقق من تغير التاريخ كل دقيقة
            const dateCheckInterval = setInterval(function() {
                try {
                    // الحصول على التاريخ الحالي
                    const today = new Date();
                    const dateKey = formatDate(today);

                    // التحقق من تغير التاريخ
                    const lastCheckedDate = localStorage.getItem('lastCheckedDate') || '';

                    if (dateKey !== lastCheckedDate) {
                        console.log('تم اكتشاف تغير في التاريخ من', lastCheckedDate, 'إلى', dateKey);

                        // تحديث المواقيت المحسوبة مع الحفاظ على التعديلات اليدوية
                        updateCalculatedTimesWithManualAdjustments();

                        // تحديث تاريخ آخر فحص
                        localStorage.setItem('lastCheckedDate', dateKey);

                        console.log('تم تحديث المواقيت بعد تغير التاريخ');
                    }
                } catch (error) {
                    console.error('خطأ في فحص تغير التاريخ:', error);
                }
            }, 60 * 1000); // كل دقيقة

            // إعداد مؤقت جديد للتحديث التلقائي كل ساعة
            autoUpdateInterval = setInterval(function() {
                try {
                    console.log('بدء التحديث التلقائي الدوري للمواقيت...');

                    // تحديث المواقيت المحسوبة مع الحفاظ على التعديلات اليدوية
                    updateCalculatedTimesWithManualAdjustments();

                    console.log('تم التحديث التلقائي الدوري للمواقيت بنجاح');
                } catch (error) {
                    console.error('خطأ في التحديث التلقائي الدوري للمواقيت:', error);
                }
            }, 60 * 60 * 1000); // كل ساعة

            console.log('تم إعداد التحديث التلقائي للمواقيت بنجاح');

            // حفظ مراجع المؤقتات للتنظيف لاحقاً
            window.autoUpdateIntervals = {
                dateCheck: dateCheckInterval,
                hourlyUpdate: autoUpdateInterval
            };

            return {
                dateCheck: dateCheckInterval,
                hourlyUpdate: autoUpdateInterval
            };
        }

        // دالة لتحديث المواقيت المحسوبة مع الحفاظ على التعديلات اليدوية
        function updateCalculatedTimesWithManualAdjustments() {
            try {
                console.log('بدء تحديث المواقيت المحسوبة مع الحفاظ على التعديلات اليدوية...');

                // الحصول على المدينة الحالية
                const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                console.log('المدينة الحالية:', currentCity);

                // إذا كانت المواقيت المعدلة يدوياً مفعلة، استخدمها
                if (manualPrayerTimesEnabled && manualPrayerTimes && manualPrayerTimes[currentCity]) {
                    console.log('المواقيت المعدلة يدوياً مفعلة للمدينة:', currentCity);

                    // الحصول على التاريخ الحالي
                    const today = new Date();
                    const dateKey = formatDate(today);
                    console.log('التاريخ الحالي:', dateKey);

                    // التحقق من وجود مواقيت محسوبة للتاريخ الحالي
                    let calculatedTimes = null;
                    if (window.prayerTimes && window.prayerTimes[currentCity]) {
                        calculatedTimes = window.prayerTimes[currentCity];
                        console.log('تم العثور على مواقيت محسوبة للمدينة الحالية:', calculatedTimes);
                    } else {
                        // محاولة حساب المواقيت للتاريخ الحالي
                        console.log('محاولة حساب المواقيت للتاريخ الحالي...');
                        try {
                            // تحديث المواقيت المحسوبة بدون تطبيق التعديلات اليدوية
                            if (typeof getPrayerTimes === 'function') {
                                getPrayerTimes();
                                if (window.prayerTimes && window.prayerTimes[currentCity]) {
                                    calculatedTimes = window.prayerTimes[currentCity];
                                    console.log('تم حساب المواقيت للتاريخ الحالي:', calculatedTimes);
                                }
                            }
                        } catch (calcError) {
                            console.error('خطأ في حساب المواقيت للتاريخ الحالي:', calcError);
                        }
                    }

                    // إذا تم الحصول على مواقيت محسوبة، قم بتحديث المواقيت المعدلة يدوياً
                    if (calculatedTimes) {
                        console.log('تحديث المواقيت المعدلة يدوياً بناءً على المواقيت المحسوبة...');

                        // نسخ المواقيت المعدلة يدوياً
                        const manualTimes = JSON.parse(JSON.stringify(manualPrayerTimes[currentCity]));

                        // تحديث المواقيت العالمية بالمواقيت المعدلة يدوياً
                        window.prayerTimes[currentCity] = manualTimes;

                        // تحديث المواقيت الثابتة أيضاً
                        if (!window.AMMAN_PRAYER_TIMES) {
                            window.AMMAN_PRAYER_TIMES = {};
                        }
                        window.AMMAN_PRAYER_TIMES[currentCity] = manualTimes;

                        console.log('تم تحديث المواقيت العالمية بالمواقيت المعدلة يدوياً');
                    }

                    // تطبيق المواقيت المعدلة يدوياً
                    applyManualPrayerTimes();
                    return;
                }

                // تحديث المواقيت المحسوبة
                console.log('تحديث المواقيت المحسوبة للمدينة:', currentCity);
                forceUpdatePrayerTimes();
            } catch (error) {
                console.error('خطأ في تحديث المواقيت المحسوبة مع الحفاظ على التعديلات اليدوية:', error);
            }
        }

        // دالة لتنسيق التاريخ (YYYY-MM-DD)
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // دالة لإعداد مستمعي الأحداث للتعديل اليدوي
        function setupManualPrayerTimeListeners() {
            try {
                // مستمع الحدث لزر حفظ جميع المواقيت المعدلة
                const saveManualTimesBtn = document.getElementById('save-manual-times');
                if (saveManualTimesBtn) {
                    console.log('تم العثور على زر حفظ المواقيت المعدلة يدوياً');

                    saveManualTimesBtn.addEventListener('click', function() {
                        try {
                            console.log('تم النقر على زر حفظ جميع المواقيت المعدلة');

                            // عرض إشعار بدء الحفظ
                            showNotification('جاري حفظ المواقيت المعدلة يدوياً...', 'info');

                            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                            console.log('المدينة الحالية:', currentCity);

                            // تهيئة المتغير إذا لم يكن موجوداً
                            if (!manualPrayerTimes) {
                                console.log('تهيئة متغير المواقيت المعدلة يدوياً');
                                manualPrayerTimes = {};
                            }

                            // إنشاء كائن للمواقيت المعدلة للمدينة الحالية
                            if (!manualPrayerTimes[currentCity]) {
                                console.log('إنشاء كائن للمواقيت المعدلة للمدينة الحالية');
                                manualPrayerTimes[currentCity] = {};
                            }

                            // قراءة القيم من حقول الإدخال
                            const fajrInput = document.getElementById('manual-fajr');
                            const sunriseInput = document.getElementById('manual-sunrise');
                            const dhuhrInput = document.getElementById('manual-dhuhr');
                            const asrInput = document.getElementById('manual-asr');
                            const maghribInput = document.getElementById('manual-maghrib');
                            const ishaInput = document.getElementById('manual-isha');

                            // التحقق من وجود حقول الإدخال
                            if (!fajrInput || !sunriseInput || !dhuhrInput || !asrInput || !maghribInput || !ishaInput) {
                                console.error('لم يتم العثور على بعض حقول الإدخال');

                                // محاولة إنشاء حقول الإدخال المفقودة
                                const missingInputs = [];
                                if (!fajrInput) missingInputs.push('الفجر');
                                if (!sunriseInput) missingInputs.push('الشروق');
                                if (!dhuhrInput) missingInputs.push('الظهر');
                                if (!asrInput) missingInputs.push('العصر');
                                if (!maghribInput) missingInputs.push('المغرب');
                                if (!ishaInput) missingInputs.push('العشاء');

                                showNotification(`حدث خطأ: لم يتم العثور على حقول الإدخال التالية: ${missingInputs.join(', ')}`, 'error');
                                return;
                            }

                            // التحقق من صحة القيم المدخلة
                            const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
                            const invalidInputs = [];

                            if (fajrInput.value && !timeRegex.test(fajrInput.value)) invalidInputs.push('الفجر');
                            if (sunriseInput.value && !timeRegex.test(sunriseInput.value)) invalidInputs.push('الشروق');
                            if (dhuhrInput.value && !timeRegex.test(dhuhrInput.value)) invalidInputs.push('الظهر');
                            if (asrInput.value && !timeRegex.test(asrInput.value)) invalidInputs.push('العصر');
                            if (maghribInput.value && !timeRegex.test(maghribInput.value)) invalidInputs.push('المغرب');
                            if (ishaInput.value && !timeRegex.test(ishaInput.value)) invalidInputs.push('العشاء');

                            if (invalidInputs.length > 0) {
                                console.error('قيم غير صالحة في حقول الإدخال:', invalidInputs);
                                showNotification(`قيم غير صالحة في حقول الإدخال التالية: ${invalidInputs.join(', ')}. يجب إدخال الوقت بتنسيق HH:MM`, 'error');
                                return;
                            }

                            // حفظ القيم في كائن المواقيت المعدلة
                            if (fajrInput.value) manualPrayerTimes[currentCity].fajr = fajrInput.value;
                            if (sunriseInput.value) manualPrayerTimes[currentCity].sunrise = sunriseInput.value;
                            if (dhuhrInput.value) manualPrayerTimes[currentCity].dhuhr = dhuhrInput.value;
                            if (asrInput.value) manualPrayerTimes[currentCity].asr = asrInput.value;
                            if (maghribInput.value) manualPrayerTimes[currentCity].maghrib = maghribInput.value;
                            if (ishaInput.value) manualPrayerTimes[currentCity].isha = ishaInput.value;

                            console.log('تم قراءة القيم من حقول الإدخال:', manualPrayerTimes[currentCity]);

                            // تفعيل المواقيت المعدلة يدوياً
                            manualPrayerTimesEnabled = true;
                            console.log('تم تفعيل المواقيت المعدلة يدوياً');

                            // حفظ المواقيت المعدلة في التخزين المحلي
                            const saveResult = saveManualPrayerTimes();
                            console.log(`نتيجة حفظ المواقيت المعدلة: ${saveResult ? 'نجاح' : 'فشل'}`);

                            // تطبيق المواقيت المعدلة
                            const applyResult = applyManualPrayerTimes();
                            console.log(`نتيجة تطبيق المواقيت المعدلة: ${applyResult ? 'نجاح' : 'فشل'}`);

                            // عرض إشعار
                            if (saveResult && applyResult) {
                                // تأخير عرض الإشعار لضمان تطبيق المواقيت أولاً
                                setTimeout(() => {
                                    showNotification('تم حفظ وتطبيق المواقيت المعدلة يدوياً بنجاح', 'success');
                                }, 500);
                                console.log('تم حفظ وتطبيق المواقيت المعدلة يدوياً بنجاح');
                            } else {
                                showNotification('حدث خطأ أثناء حفظ أو تطبيق المواقيت المعدلة يدوياً', 'error');
                                console.error('حدث خطأ أثناء حفظ أو تطبيق المواقيت المعدلة يدوياً');
                            }
                        } catch (error) {
                            console.error('خطأ في حفظ المواقيت المعدلة يدوياً:', error);
                            showNotification('حدث خطأ أثناء حفظ المواقيت المعدلة يدوياً', 'error');
                        }
                    });
                } else {
                    console.error('لم يتم العثور على زر حفظ المواقيت المعدلة يدوياً');
                }

                // مستمع الحدث لزر إعادة ضبط المواقيت
                document.getElementById('reset-manual-times').addEventListener('click', function() {
                    try {
                        // عرض رسالة تأكيد
                        if (confirm('هل تريد إعادة ضبط المواقيت وإلغاء التعديلات اليدوية؟')) {
                            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

                            // إلغاء تفعيل المواقيت المعدلة يدوياً
                            manualPrayerTimesEnabled = false;

                            // حذف المواقيت المعدلة للمدينة الحالية
                            if (manualPrayerTimes[currentCity]) {
                                delete manualPrayerTimes[currentCity];
                            }

                            // حفظ التغييرات
                            saveManualPrayerTimes();

                            // تحديث المواقيت المحسوبة
                            forceUpdatePrayerTimes();

                            // تحديث حقول التعديل اليدوي
                            updateManualPrayerTimeInputs();

                            // عرض إشعار
                            setTimeout(() => {
                                showNotification('تم إعادة ضبط المواقيت وإلغاء التعديلات اليدوية بنجاح', 'success');
                            }, 500);
                        }
                    } catch (error) {
                        console.error('خطأ في إعادة ضبط المواقيت:', error);
                        showNotification('حدث خطأ أثناء إعادة ضبط المواقيت', 'error');
                    }
                });

                // مستمعي الأحداث لأزرار تعديل كل صلاة
                const editButtons = document.querySelectorAll('.edit-prayer-time-btn');
                console.log(`تم العثور على ${editButtons.length} زر تعديل للمواقيت`);

                editButtons.forEach(function(button) {
                    button.addEventListener('click', function() {
                        try {
                            const prayer = this.getAttribute('data-prayer');
                            console.log(`تم النقر على زر تعديل وقت ${prayer}`);

                            const inputId = 'manual-' + prayer;
                            const inputElement = document.getElementById(inputId);

                            if (!inputElement) {
                                console.error(`لم يتم العثور على عنصر الإدخال بالمعرف: ${inputId}`);
                                showNotification(`لم يتم العثور على حقل الإدخال لوقت ${prayer}`, 'error');
                                return;
                            }

                            const inputValue = inputElement.value;
                            console.log(`قيمة حقل الإدخال: ${inputValue}`);

                            if (!inputValue) {
                                showNotification('الرجاء إدخال وقت صحيح', 'error');
                                return;
                            }

                            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                            console.log(`المدينة الحالية: ${currentCity}`);

                            // تهيئة المتغير إذا لم يكن موجوداً
                            if (!manualPrayerTimes) {
                                console.log('تهيئة متغير المواقيت المعدلة يدوياً');
                                manualPrayerTimes = {};
                            }

                            // إنشاء كائن للمواقيت المعدلة للمدينة الحالية إذا لم يكن موجوداً
                            if (!manualPrayerTimes[currentCity]) {
                                console.log('إنشاء كائن للمواقيت المعدلة للمدينة الحالية');
                                manualPrayerTimes[currentCity] = {};

                                // نسخ المواقيت الحالية
                                const currentTimes = window.prayerTimes?.[currentCity] || window.AMMAN_PRAYER_TIMES?.[currentCity] || {};
                                console.log('المواقيت الحالية:', currentTimes);

                                // نسخ المواقيت الحالية إلى المواقيت المعدلة
                                Object.keys(currentTimes).forEach(key => {
                                    manualPrayerTimes[currentCity][key] = currentTimes[key];
                                });

                                console.log('تم نسخ المواقيت الحالية إلى المواقيت المعدلة');
                            }

                            // تحديث وقت الصلاة المحدد
                            manualPrayerTimes[currentCity][prayer] = inputValue;
                            console.log(`تم تحديث وقت ${prayer} إلى ${inputValue}`);

                            // تفعيل المواقيت المعدلة يدوياً
                            manualPrayerTimesEnabled = true;
                            console.log('تم تفعيل المواقيت المعدلة يدوياً');

                            // حفظ المواقيت المعدلة في التخزين المحلي
                            const saveResult = saveManualPrayerTimes();
                            console.log(`نتيجة حفظ المواقيت المعدلة: ${saveResult ? 'نجاح' : 'فشل'}`);

                            // تطبيق المواقيت المعدلة
                            const applyResult = applyManualPrayerTimes();
                            console.log(`نتيجة تطبيق المواقيت المعدلة: ${applyResult ? 'نجاح' : 'فشل'}`);

                            // عرض إشعار
                            const prayerNames = {
                                fajr: 'الفجر',
                                sunrise: 'الشروق',
                                dhuhr: 'الظهر',
                                asr: 'العصر',
                                maghrib: 'المغرب',
                                isha: 'العشاء'
                            };

                            // تأخير عرض الإشعار لضمان تطبيق المواقيت أولاً
                            setTimeout(() => {
                                showNotification(`تم تعديل وقت ${prayerNames[prayer]} إلى ${inputValue} بنجاح`, 'success');
                            }, 500);
                        } catch (error) {
                            console.error('خطأ في تعديل وقت الصلاة:', error);
                            showNotification('حدث خطأ أثناء تعديل وقت الصلاة', 'error');
                        }
                    });
                });

                console.log('تم إعداد مستمعي الأحداث للتعديل اليدوي بنجاح');
            } catch (error) {
                console.error('خطأ في إعداد مستمعي الأحداث للتعديل اليدوي:', error);
            }
        }

        // تحميل المواقيت المعدلة يدوياً وإعداد التحديث التلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('بدء تهيئة نظام التعديل اليدوي للمواقيت...');

            // تهيئة المتغيرات العالمية إذا لم تكن موجودة
            if (typeof manualPrayerTimes === 'undefined') {
                window.manualPrayerTimes = {};
            }

            if (typeof manualPrayerTimesEnabled === 'undefined') {
                window.manualPrayerTimesEnabled = localStorage.getItem('manualPrayerTimesEnabled') === 'true';
            }

            // تحميل المواقيت المعدلة يدوياً
            const loadResult = loadManualPrayerTimes();
            console.log(`نتيجة تحميل المواقيت المعدلة يدوياً: ${loadResult ? 'نجاح' : 'لا توجد مواقيت محفوظة'}`);

            // تحديث حقول التعديل اليدوي وإعداد مستمعي الأحداث بعد تأخير قصير
            // للتأكد من تحميل جميع عناصر الصفحة
            setTimeout(function() {
                try {
                    console.log('تحديث حقول التعديل اليدوي...');
                    if (typeof updateManualPrayerTimeInputs === 'function') {
                        updateManualPrayerTimeInputs();
                    } else {
                        console.warn('دالة updateManualPrayerTimeInputs غير متوفرة');
                    }

                    console.log('إعداد مستمعي الأحداث للتعديل اليدوي...');
                    if (typeof setupManualPrayerTimeListeners === 'function') {
                        setupManualPrayerTimeListeners();
                    } else {
                        console.warn('دالة setupManualPrayerTimeListeners غير متوفرة');
                    }

                    console.log('إعداد التحديث التلقائي...');
                    if (typeof setupAutoUpdate === 'function') {
                        setupAutoUpdate();
                    } else {
                        console.warn('دالة setupAutoUpdate غير متوفرة');
                    }

                    // تطبيق المواقيت المعدلة يدوياً إذا كانت مفعلة
                    if (manualPrayerTimesEnabled) {
                        console.log('تطبيق المواقيت المعدلة يدوياً...');
                        if (typeof applyManualPrayerTimes === 'function') {
                            const applyResult = applyManualPrayerTimes();
                            console.log(`نتيجة تطبيق المواقيت المعدلة يدوياً: ${applyResult ? 'نجاح' : 'فشل'}`);
                        } else {
                            console.warn('دالة applyManualPrayerTimes غير متوفرة');
                        }
                    }

                    // تحديث العرض
                    if (typeof updatePrayerTimes === 'function') {
                        updatePrayerTimes();
                        console.log('تم تحديث عرض مواقيت الصلاة');
                    }

                    // تحديث العد التنازلي والصلاة القادمة
                    if (typeof displayRemainingPrayerTimes === 'function') {
                        displayRemainingPrayerTimes();
                        console.log('تم تحديث العد التنازلي للصلاة القادمة');
                    }

                    console.log('تم تهيئة نظام التعديل اليدوي للمواقيت بنجاح');
                } catch (error) {
                    console.error('خطأ في تهيئة نظام التعديل اليدوي للمواقيت:', error);
                }
            }, 2000); // زيادة التأخير إلى 2 ثانية للتأكد من تحميل جميع العناصر
        });

        // إعدادات الألوان
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تهيئة نظام إعدادات الألوان...');

            // تحميل الألوان المحفوظة
            function loadSavedColors() {
                try {
                    const savedColors = localStorage.getItem('customColors');
                    if (savedColors) {
                        const colors = JSON.parse(savedColors);
                        console.log('تم تحميل الألوان المحفوظة:', colors);

                        // تطبيق الألوان المحفوظة على حقول الإدخال
                        Object.keys(colors).forEach(colorKey => {
                            const inputElement = document.getElementById(colorKey);
                            if (inputElement) {
                                inputElement.value = colors[colorKey];
                            }
                        });

                        // تطبيق الألوان على العناصر
                        applyColors(colors);
                        return true;
                    }
                } catch (error) {
                    console.error('خطأ في تحميل الألوان المحفوظة:', error);
                }
                return false;
            }

            // تطبيق الألوان على العناصر
            function applyColors(colors) {
                try {
                    console.log('تطبيق الألوان على العناصر...');

                    // تحديث متغيرات CSS
                    const root = document.documentElement;

                    // ألوان الشريط الجانبي
                    if (colors['sidebar-bg-color']) {
                        root.style.setProperty('--dark-pink', colors['sidebar-bg-color']);
                        // تطبيق على الشريط الجانبي مباشرة
                        const sidebar = document.querySelector('.vertical-panel');
                        if (sidebar) {
                            sidebar.style.backgroundColor = colors['sidebar-bg-color'];
                        }
                    }

                    if (colors['sidebar-text-color']) {
                        root.style.setProperty('--gold-color', colors['sidebar-text-color']);
                    }

                    // ألوان شريط مواقيت الصلاة
                    if (colors['prayer-bar-bg-color']) {
                        const prayerBar = document.querySelector('.prayer-times');
                        if (prayerBar) {
                            prayerBar.style.backgroundColor = colors['prayer-bar-bg-color'];
                        }
                    }

                    if (colors['prayer-bar-text-color']) {
                        const prayerBar = document.querySelector('.prayer-times');
                        if (prayerBar) {
                            prayerBar.style.color = colors['prayer-bar-text-color'];
                        }
                    }

                    if (colors['prayer-time-color']) {
                        // تطبيق لون أوقات الصلاة
                        const prayerTimeElements = document.querySelectorAll('.prayer-times .prayer-hour, .prayer-times [id$="-time"]');
                        prayerTimeElements.forEach(element => {
                            const spans = element.querySelectorAll('span');
                            spans.forEach(span => {
                                span.style.color = colors['prayer-time-color'];
                            });
                        });
                    }

                    // ألوان الساعة الرقمية
                    if (colors['digital-clock-bg-color'] || colors['digital-clock-text-color'] || colors['digital-clock-border-color']) {
                        const digitalClock = document.querySelector('.digital-clock');
                        if (digitalClock) {
                            if (colors['digital-clock-bg-color']) {
                                digitalClock.style.backgroundColor = colors['digital-clock-bg-color'];
                            }
                            if (colors['digital-clock-text-color']) {
                                digitalClock.style.color = colors['digital-clock-text-color'];
                            }
                            if (colors['digital-clock-border-color']) {
                                digitalClock.style.borderColor = colors['digital-clock-border-color'];
                            }
                        }
                    }

                    // ألوان العد التنازلي
                    if (colors['countdown-bg-color'] || colors['countdown-border-color']) {
                        const countdownCircle = document.querySelector('.countdown-circle');
                        if (countdownCircle) {
                            if (colors['countdown-bg-color']) {
                                countdownCircle.style.backgroundColor = colors['countdown-bg-color'];
                            }
                            if (colors['countdown-border-color']) {
                                countdownCircle.style.borderColor = colors['countdown-border-color'];
                            }
                        }
                    }

                    console.log('تم تطبيق الألوان بنجاح');
                } catch (error) {
                    console.error('خطأ في تطبيق الألوان:', error);
                }
            }

            // حفظ الألوان
            function saveColors() {
                try {
                    console.log('حفظ الألوان...');

                    const colors = {
                        'sidebar-bg-color': document.getElementById('sidebar-bg-color').value,
                        'sidebar-text-color': document.getElementById('sidebar-text-color').value,
                        'prayer-bar-bg-color': document.getElementById('prayer-bar-bg-color').value,
                        'prayer-bar-text-color': document.getElementById('prayer-bar-text-color').value,
                        'prayer-time-color': document.getElementById('prayer-time-color').value,
                        'digital-clock-bg-color': document.getElementById('digital-clock-bg-color').value,
                        'digital-clock-text-color': document.getElementById('digital-clock-text-color').value,
                        'digital-clock-border-color': document.getElementById('digital-clock-border-color').value,
                        'countdown-bg-color': document.getElementById('countdown-bg-color').value,
                        'countdown-border-color': document.getElementById('countdown-border-color').value
                    };

                    // حفظ الألوان في التخزين المحلي
                    localStorage.setItem('customColors', JSON.stringify(colors));
                    console.log('تم حفظ الألوان:', colors);

                    // تطبيق الألوان
                    applyColors(colors);

                    // عرض رسالة تأكيد
                    alert('تم حفظ الألوان بنجاح');

                    return true;
                } catch (error) {
                    console.error('خطأ في حفظ الألوان:', error);
                    alert('حدث خطأ أثناء حفظ الألوان');
                    return false;
                }
            }

            // إعادة ضبط الألوان
            function resetColors() {
                try {
                    console.log('إعادة ضبط الألوان...');

                    // الألوان الافتراضية
                    const defaultColors = {
                        'sidebar-bg-color': '#4a3b3b',
                        'sidebar-text-color': '#71d3ee',
                        'prayer-bar-bg-color': '#4a3b3b',
                        'prayer-bar-text-color': '#71d3ee',
                        'prayer-time-color': '#40E0D0',
                        'digital-clock-bg-color': '#000000',
                        'digital-clock-text-color': '#71d3ee',
                        'digital-clock-border-color': '#71d3ee',
                        'countdown-bg-color': '#ffffff',
                        'countdown-border-color': '#71d3ee'
                    };

                    // تطبيق الألوان الافتراضية على حقول الإدخال
                    Object.keys(defaultColors).forEach(colorKey => {
                        const inputElement = document.getElementById(colorKey);
                        if (inputElement) {
                            inputElement.value = defaultColors[colorKey];
                        }
                    });

                    // حذف الألوان المحفوظة
                    localStorage.removeItem('customColors');
                    console.log('تم حذف الألوان المحفوظة');

                    // تطبيق الألوان الافتراضية
                    applyColors(defaultColors);

                    // عرض رسالة تأكيد
                    alert('تم إعادة ضبط الألوان إلى القيم الافتراضية');

                    return true;
                } catch (error) {
                    console.error('خطأ في إعادة ضبط الألوان:', error);
                    alert('حدث خطأ أثناء إعادة ضبط الألوان');
                    return false;
                }
            }

            // إضافة مستمعي الأحداث
            const saveColorsBtn = document.getElementById('save-colors');
            const resetColorsBtn = document.getElementById('reset-colors');

            if (saveColorsBtn) {
                saveColorsBtn.addEventListener('click', saveColors);
                console.log('تم إضافة مستمع الحدث لزر حفظ الألوان');
            } else {
                console.error('لم يتم العثور على زر حفظ الألوان');
            }

            if (resetColorsBtn) {
                resetColorsBtn.addEventListener('click', function() {
                    if (confirm('هل تريد إعادة ضبط الألوان إلى القيم الافتراضية؟')) {
                        resetColors();
                    }
                });
                console.log('تم إضافة مستمع الحدث لزر إعادة ضبط الألوان');
            } else {
                console.error('لم يتم العثور على زر إعادة ضبط الألوان');
            }

            // تحميل الألوان المحفوظة عند تحميل الصفحة
            setTimeout(() => {
                const loaded = loadSavedColors();
                if (loaded) {
                    console.log('تم تحميل وتطبيق الألوان المحفوظة');
                } else {
                    console.log('لم يتم العثور على ألوان محفوظة، استخدام الألوان الافتراضية');
                }
            }, 1000);

            console.log('تم تهيئة نظام إعدادات الألوان بنجاح');
        });
    </script>

    <script src="clock.js"></script>

    <!-- ملف إصلاح المشاكل الأساسية -->
    <script src="fix-issues.js"></script>

    <!-- ملف تشخيص المشاكل -->
    <script src="debug.js"></script>

    <!-- الإصلاح السريع والمباشر -->
    <script src="quick-fix.js"></script>

    <!-- إصلاح نظام التعتيم -->
    <script src="darkness-fix.js"></script>

    <!-- إصلاح مدة التعتيم المباشر -->
    <script src="darkness-duration-fix.js"></script>

    <!-- إصلاح المشاكل الصوتية -->
    <script src="create-audio-files.js"></script>

    <!-- الإصلاح النهائي لمدة التعتيم -->
    <script src="final-darkness-fix.js"></script>

    <!-- PWA Support - لا يؤثر على التصميم -->
    <script src="pwa-simple.js"></script>
</body>
</html> فارغ