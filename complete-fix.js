// إصلاح شامل ومباشر لمدة الإقامة والتعتيم
console.log('🔧 بدء الإصلاح الشامل لمدة الإقامة والتعتيم...');

// إيقاف جميع الإصلاحات السابقة
window.fixingInProgress = true;

// دالة للحصول على مدة الإقامة
function getIqamahDuration(prayerName) {
    console.log(`🕌 البحث عن مدة الإقامة لصلاة ${prayerName}...`);
    
    // البحث في localStorage أولاً
    try {
        const saved = localStorage.getItem('iqama_durations');
        if (saved) {
            const times = JSON.parse(saved);
            if (times[prayerName]) {
                console.log(`✅ مدة الإقامة من localStorage: ${times[prayerName]} دقيقة`);
                return parseInt(times[prayerName]);
            }
        }
    } catch (e) {}
    
    // البحث في حقول الإدخال
    const input = document.getElementById(`${prayerName}-iqama-duration`);
    if (input && input.value) {
        console.log(`✅ مدة الإقامة من الحقل: ${input.value} دقيقة`);
        return parseInt(input.value);
    }
    
    console.log(`⚠️ استخدام مدة الإقامة الافتراضية: 10 دقائق`);
    return 10;
}

// دالة للحصول على مدة التعتيم
function getDarknessDuration(prayerName) {
    console.log(`🌙 البحث عن مدة التعتيم لصلاة ${prayerName}...`);
    
    // البحث في localStorage أولاً
    try {
        const saved = localStorage.getItem('darknessTimes');
        if (saved) {
            const times = JSON.parse(saved);
            if (times[prayerName]) {
                console.log(`✅ مدة التعتيم من localStorage: ${times[prayerName]} دقيقة`);
                return parseInt(times[prayerName]);
            }
        }
    } catch (e) {}
    
    // البحث في حقول الإدخال
    const possibleIds = [`${prayerName}-darkness`, `${prayerName}-darkness-duration`];
    for (const id of possibleIds) {
        const input = document.getElementById(id);
        if (input && input.value) {
            console.log(`✅ مدة التعتيم من حقل ${id}: ${input.value} دقيقة`);
            return parseInt(input.value);
        }
    }
    
    console.log(`⚠️ استخدام مدة التعتيم الافتراضية: 10 دقائق`);
    return 10;
}

// إعادة تعريف دالة بدء العد التنازلي للإقامة
function fixStartIqamahCountdown() {
    console.log('🔧 إعادة تعريف دالة بدء العد التنازلي للإقامة...');
    
    window.startIqamahCountdown = function(prayerName, arabicName) {
        console.log(`🕌 بدء العد التنازلي للإقامة - ${arabicName} (${prayerName})`);
        
        // الحصول على مدة الإقامة الصحيحة
        const iqamahMinutes = getIqamahDuration(prayerName);
        console.log(`⏰ مدة الإقامة: ${iqamahMinutes} دقيقة`);
        
        // الحصول على مدة التعتيم الصحيحة
        const darknessMinutes = getDarknessDuration(prayerName);
        console.log(`🌙 مدة التعتيم: ${darknessMinutes} دقيقة`);
        
        // تحديث المتغيرات العالمية
        window.currentIqamahMinutes = iqamahMinutes;
        window.currentDarknessMinutes = darknessMinutes;
        
        // إشعار للمستخدم
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #4CAF50;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            z-index: 10000;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        `;
        notification.innerHTML = `
            <div>🕌 بدء العد التنازلي لصلاة ${arabicName}</div>
            <div style="font-size: 12px; margin-top: 5px;">
                ⏰ الإقامة: ${iqamahMinutes} دقيقة | 🌙 التعتيم: ${darknessMinutes} دقيقة
            </div>
        `;
        document.body.appendChild(notification);
        
        setTimeout(() => notification.remove(), 5000);
        
        // بدء العد التنازلي للإقامة
        startIqamahCountdownTimer(prayerName, arabicName, iqamahMinutes, darknessMinutes);
    };
    
    console.log('✅ تم إعادة تعريف دالة بدء العد التنازلي للإقامة');
}

// دالة العد التنازلي للإقامة
function startIqamahCountdownTimer(prayerName, arabicName, iqamahMinutes, darknessMinutes) {
    console.log(`⏰ بدء العد التنازلي: ${iqamahMinutes} دقيقة للإقامة`);
    
    let remainingSeconds = iqamahMinutes * 60;
    
    // إنشاء عنصر العد التنازلي
    let countdownElement = document.getElementById('iqamah-countdown');
    if (!countdownElement) {
        countdownElement = document.createElement('div');
        countdownElement.id = 'iqamah-countdown';
        countdownElement.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #40E0D0;
            padding: 30px 50px;
            border-radius: 15px;
            z-index: 9999;
            font-size: 3em;
            font-weight: bold;
            text-align: center;
            border: 3px solid #40E0D0;
            box-shadow: 0 0 30px rgba(64, 224, 208, 0.5);
        `;
        document.body.appendChild(countdownElement);
    }
    
    // تحديث العد التنازلي
    const updateCountdown = () => {
        const minutes = Math.floor(remainingSeconds / 60);
        const seconds = remainingSeconds % 60;
        
        countdownElement.innerHTML = `
            <div>🕌 صلاة ${arabicName}</div>
            <div style="font-size: 1.5em; margin: 20px 0;">
                ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}
            </div>
            <div style="font-size: 0.4em;">الإقامة خلال</div>
        `;
        
        remainingSeconds--;
        
        if (remainingSeconds < 0) {
            clearInterval(window.iqamahCountdownInterval);
            countdownElement.remove();
            
            // بدء التعتيم
            startDarknessMode(prayerName, arabicName, darknessMinutes);
        }
    };
    
    // بدء العد التنازلي
    updateCountdown();
    window.iqamahCountdownInterval = setInterval(updateCountdown, 1000);
}

// دالة بدء التعتيم
function startDarknessMode(prayerName, arabicName, darknessMinutes) {
    console.log(`🌙 بدء التعتيم لمدة ${darknessMinutes} دقيقة`);
    
    // إنشاء شاشة التعتيم
    let darknessOverlay = document.getElementById('darkness-overlay');
    if (!darknessOverlay) {
        darknessOverlay = document.createElement('div');
        darknessOverlay.id = 'darkness-overlay';
        darknessOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: black;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        `;
        document.body.appendChild(darknessOverlay);
    }
    
    // إضافة الساعة الرقمية
    const digitalClock = document.createElement('div');
    digitalClock.style.cssText = `
        color: white;
        font-size: 15vw;
        font-weight: bold;
        text-align: center;
        font-family: Arial, sans-serif;
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
    `;
    darknessOverlay.appendChild(digitalClock);
    
    // إضافة معلومات التعتيم
    const darknessInfo = document.createElement('div');
    darknessInfo.style.cssText = `
        color: #40E0D0;
        font-size: 2em;
        margin-top: 30px;
        text-align: center;
    `;
    darknessInfo.textContent = `🌙 التعتيم لمدة ${darknessMinutes} دقيقة`;
    darknessOverlay.appendChild(darknessInfo);
    
    // إضافة زر الإغلاق
    const closeButton = document.createElement('button');
    closeButton.style.cssText = `
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-size: 24px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        cursor: pointer;
    `;
    closeButton.textContent = '×';
    closeButton.onclick = () => {
        clearInterval(window.darknessClockInterval);
        darknessOverlay.remove();
    };
    darknessOverlay.appendChild(closeButton);
    
    // تحديث الساعة
    const updateClock = () => {
        const now = new Date();
        const time = now.toLocaleTimeString('ar-SA', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        digitalClock.textContent = time;
    };
    
    updateClock();
    window.darknessClockInterval = setInterval(updateClock, 1000);
    
    // إنهاء التعتيم بعد المدة المحددة
    setTimeout(() => {
        clearInterval(window.darknessClockInterval);
        darknessOverlay.style.opacity = '0';
        setTimeout(() => {
            darknessOverlay.remove();
            console.log('✅ تم إنهاء التعتيم');
        }, 1000);
    }, darknessMinutes * 60 * 1000);
    
    console.log(`🌙 سيتم إنهاء التعتيم بعد ${darknessMinutes} دقيقة`);
}

// إصلاح أزرار الحفظ
function fixSaveButtons() {
    console.log('🔧 إصلاح أزرار الحفظ...');
    
    // إصلاح زر حفظ مدة الإقامة
    const saveIqamahBtn = document.getElementById('save-iqamah-times') || 
                          document.querySelector('button[onclick*="saveIqamahTimes"]');
    
    if (saveIqamahBtn) {
        const newSaveIqamahBtn = saveIqamahBtn.cloneNode(true);
        saveIqamahBtn.parentNode.replaceChild(newSaveIqamahBtn, saveIqamahBtn);
        
        newSaveIqamahBtn.onclick = function() {
            console.log('💾 حفظ مدة الإقامة...');
            
            const iqamahTimes = {
                fajr: parseInt(document.getElementById('fajr-iqama-duration')?.value) || 10,
                dhuhr: parseInt(document.getElementById('dhuhr-iqama-duration')?.value) || 10,
                asr: parseInt(document.getElementById('asr-iqama-duration')?.value) || 10,
                maghrib: parseInt(document.getElementById('maghrib-iqama-duration')?.value) || 10,
                isha: parseInt(document.getElementById('isha-iqama-duration')?.value) || 10
            };
            
            localStorage.setItem('iqama_durations', JSON.stringify(iqamahTimes));
            window.iqamahTimes = iqamahTimes;
            
            console.log('✅ تم حفظ مدة الإقامة:', iqamahTimes);
            alert('تم حفظ مدة الإقامة بنجاح!\n' + 
                  Object.keys(iqamahTimes).map(p => `${p}: ${iqamahTimes[p]} دقيقة`).join('\n'));
        };
    }
    
    // إصلاح زر حفظ مدة التعتيم
    const saveDarknessBtn = document.getElementById('save-darkness-times') ||
                           document.querySelector('button[onclick*="saveDarknessTimes"]');
    
    if (saveDarknessBtn) {
        const newSaveDarknessBtn = saveDarknessBtn.cloneNode(true);
        saveDarknessBtn.parentNode.replaceChild(newSaveDarknessBtn, saveDarknessBtn);
        
        newSaveDarknessBtn.onclick = function() {
            console.log('💾 حفظ مدة التعتيم...');
            
            const darknessTimes = {
                fajr: parseInt(document.getElementById('fajr-darkness')?.value || 
                              document.getElementById('fajr-darkness-duration')?.value) || 10,
                dhuhr: parseInt(document.getElementById('dhuhr-darkness')?.value || 
                               document.getElementById('dhuhr-darkness-duration')?.value) || 10,
                asr: parseInt(document.getElementById('asr-darkness')?.value || 
                             document.getElementById('asr-darkness-duration')?.value) || 10,
                maghrib: parseInt(document.getElementById('maghrib-darkness')?.value || 
                                 document.getElementById('maghrib-darkness-duration')?.value) || 10,
                isha: parseInt(document.getElementById('isha-darkness')?.value || 
                              document.getElementById('isha-darkness-duration')?.value) || 10
            };
            
            localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
            window.darknessTimes = darknessTimes;
            
            console.log('✅ تم حفظ مدة التعتيم:', darknessTimes);
            alert('تم حفظ مدة التعتيم بنجاح!\n' + 
                  Object.keys(darknessTimes).map(p => `${p}: ${darknessTimes[p]} دقيقة`).join('\n'));
        };
    }
    
    console.log('✅ تم إصلاح أزرار الحفظ');
}

// تحميل الإعدادات المحفوظة
function loadSavedSettings() {
    console.log('📁 تحميل الإعدادات المحفوظة...');
    
    // تحميل مدة الإقامة
    try {
        const savedIqamah = localStorage.getItem('iqama_durations');
        if (savedIqamah) {
            const iqamahTimes = JSON.parse(savedIqamah);
            window.iqamahTimes = iqamahTimes;
            
            Object.keys(iqamahTimes).forEach(prayer => {
                const input = document.getElementById(`${prayer}-iqama-duration`);
                if (input) input.value = iqamahTimes[prayer];
            });
            
            console.log('✅ تم تحميل مدة الإقامة:', iqamahTimes);
        }
    } catch (e) {}
    
    // تحميل مدة التعتيم
    try {
        const savedDarkness = localStorage.getItem('darknessTimes');
        if (savedDarkness) {
            const darknessTimes = JSON.parse(savedDarkness);
            window.darknessTimes = darknessTimes;
            
            Object.keys(darknessTimes).forEach(prayer => {
                const input1 = document.getElementById(`${prayer}-darkness`);
                const input2 = document.getElementById(`${prayer}-darkness-duration`);
                if (input1) input1.value = darknessTimes[prayer];
                if (input2) input2.value = darknessTimes[prayer];
            });
            
            console.log('✅ تم تحميل مدة التعتيم:', darknessTimes);
        }
    } catch (e) {}
}

// تشغيل الإصلاح الشامل
function runCompleteFix() {
    console.log('🚀 تشغيل الإصلاح الشامل...');
    
    loadSavedSettings();
    fixStartIqamahCountdown();
    fixSaveButtons();
    
    console.log('✅ تم تطبيق الإصلاح الشامل بنجاح');
    
    // إشعار نهائي
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: #4CAF50;
        color: white;
        padding: 20px 30px;
        border-radius: 10px;
        z-index: 10000;
        font-weight: bold;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    `;
    notification.innerHTML = `
        <div>✅ تم إصلاح مدة الإقامة والتعتيم</div>
        <div style="font-size: 12px; margin-top: 5px;">النظام جاهز للاستخدام</div>
    `;
    document.body.appendChild(notification);
    
    setTimeout(() => notification.remove(), 5000);
}

// تشغيل الإصلاح عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(runCompleteFix, 5000);
    });
} else {
    setTimeout(runCompleteFix, 5000);
}

console.log('✅ تم تحميل الإصلاح الشامل لمدة الإقامة والتعتيم');
