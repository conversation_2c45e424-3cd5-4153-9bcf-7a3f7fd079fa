// إصلاح شامل لمدة التعتيم
console.log('🌙 بدء إصلاح نظام التعتيم...');

// دالة إصلاح شاملة لمدة التعتيم
function fixDarknessSystem() {
    console.log('🔧 إصلاح نظام التعتيم بالكامل...');
    
    // 1. إصلاح حفظ مدة التعتيم
    function setupDarknessSave() {
        const saveBtn = document.getElementById('save-darkness-times');
        if (saveBtn) {
            // إزالة المستمعات السابقة
            const newSaveBtn = saveBtn.cloneNode(true);
            saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);
            
            newSaveBtn.addEventListener('click', function() {
                console.log('💾 حفظ إعدادات التعتيم...');
                
                // جمع القيم من جميع الحقول الممكنة
                const darknessTimes = {};
                const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
                
                prayers.forEach(prayer => {
                    // البحث في جميع أشكال أسماء الحقول
                    let value = 10; // قيمة افتراضية
                    
                    const possibleIds = [
                        `${prayer}-darkness`,
                        `${prayer}-darkness-duration`,
                        `${prayer}_darkness`,
                        `darkness-${prayer}`
                    ];
                    
                    for (const id of possibleIds) {
                        const input = document.getElementById(id);
                        if (input && input.value) {
                            value = parseInt(input.value) || 10;
                            break;
                        }
                    }
                    
                    darknessTimes[prayer] = value;
                    console.log(`${prayer}: ${value} دقيقة`);
                });
                
                // حفظ في التخزين المحلي
                localStorage.setItem('darknessTimes', JSON.stringify(darknessTimes));
                
                // تحديث المتغيرات العالمية
                window.darknessTimes = darknessTimes;
                
                console.log('✅ تم حفظ مدة التعتيم:', darknessTimes);
                
                // إشعار للمستخدم
                showDarknessNotification('تم حفظ مدة التعتيم بنجاح', 'success');
                
                return true;
            });
            
            console.log('✅ تم إعداد زر حفظ مدة التعتيم');
        } else {
            console.warn('⚠️ لم يتم العثور على زر حفظ مدة التعتيم');
        }
    }
    
    // 2. إصلاح تحميل مدة التعتيم
    function setupDarknessLoad() {
        try {
            const savedDarkness = localStorage.getItem('darknessTimes');
            let darknessTimes;
            
            if (savedDarkness) {
                darknessTimes = JSON.parse(savedDarkness);
                console.log('📁 تم تحميل مدة التعتيم المحفوظة:', darknessTimes);
            } else {
                // قيم افتراضية
                darknessTimes = {
                    fajr: 10,
                    dhuhr: 10,
                    asr: 10,
                    maghrib: 10,
                    isha: 10
                };
                console.log('🔧 استخدام مدة التعتيم الافتراضية');
            }
            
            // تحديث المتغير العالمي
            window.darknessTimes = darknessTimes;
            
            // تحديث جميع حقول الإدخال الممكنة
            Object.keys(darknessTimes).forEach(prayer => {
                const possibleIds = [
                    `${prayer}-darkness`,
                    `${prayer}-darkness-duration`,
                    `${prayer}_darkness`,
                    `darkness-${prayer}`
                ];
                
                possibleIds.forEach(id => {
                    const input = document.getElementById(id);
                    if (input) {
                        input.value = darknessTimes[prayer];
                        console.log(`تحديث ${id}: ${darknessTimes[prayer]}`);
                    }
                });
            });
            
            console.log('✅ تم تحميل وتطبيق مدة التعتيم');
            
        } catch (error) {
            console.error('❌ خطأ في تحميل مدة التعتيم:', error);
            
            // قيم افتراضية في حالة الخطأ
            window.darknessTimes = {
                fajr: 10,
                dhuhr: 10,
                asr: 10,
                maghrib: 10,
                isha: 10
            };
        }
    }
    
    // 3. إصلاح تطبيق مدة التعتيم في النظام
    function setupDarknessApplication() {
        // التأكد من أن النظام يستخدم القيم المحفوظة
        const originalStartIqamahCountdown = window.startIqamahCountdown;
        
        if (typeof originalStartIqamahCountdown === 'function') {
            window.startIqamahCountdown = function(prayerName, arabicName) {
                console.log(`🕌 بدء العد التنازلي للإقامة - ${arabicName}`);
                
                // التأكد من وجود مدة التعتيم
                if (!window.darknessTimes) {
                    window.darknessTimes = {
                        fajr: 10,
                        dhuhr: 10,
                        asr: 10,
                        maghrib: 10,
                        isha: 10
                    };
                }
                
                const darknessMinutes = window.darknessTimes[prayerName] || 10;
                console.log(`🌙 مدة التعتيم لصلاة ${arabicName}: ${darknessMinutes} دقيقة`);
                
                // استدعاء الدالة الأصلية
                return originalStartIqamahCountdown.call(this, prayerName, arabicName);
            };
            
            console.log('✅ تم إصلاح تطبيق مدة التعتيم في النظام');
        }
    }
    
    // 4. دالة عرض الإشعارات
    function showDarknessNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideInLeft 0.3s ease;
        `;
        
        switch (type) {
            case 'success':
                notification.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                break;
            case 'error':
                notification.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                break;
            default:
                notification.style.background = 'linear-gradient(135deg, #2196F3, #1976D2)';
        }
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
    
    // تشغيل جميع الإصلاحات
    setupDarknessSave();
    setupDarknessLoad();
    setupDarknessApplication();
    
    console.log('✅ تم إصلاح نظام التعتيم بالكامل');
    
    // إشعار نهائي
    showDarknessNotification('تم إصلاح نظام التعتيم بنجاح', 'success');
}

// دالة اختبار مدة التعتيم
function testDarknessSystem() {
    console.log('🧪 اختبار نظام التعتيم...');
    
    // عرض القيم الحالية
    console.log('📊 مدة التعتيم الحالية:');
    if (window.darknessTimes) {
        Object.keys(window.darknessTimes).forEach(prayer => {
            console.log(`- ${prayer}: ${window.darknessTimes[prayer]} دقيقة`);
        });
    } else {
        console.log('❌ لم يتم العثور على إعدادات التعتيم');
    }
    
    // فحص حقول الإدخال
    console.log('📊 حقول إدخال مدة التعتيم:');
    const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
    prayers.forEach(prayer => {
        const input = document.getElementById(`${prayer}-darkness`);
        if (input) {
            console.log(`- ${prayer}: ${input.value} (حقل موجود)`);
        } else {
            console.log(`- ${prayer}: حقل غير موجود`);
        }
    });
    
    // فحص زر الحفظ
    const saveBtn = document.getElementById('save-darkness-times');
    if (saveBtn) {
        console.log('✅ زر حفظ مدة التعتيم موجود');
    } else {
        console.log('❌ زر حفظ مدة التعتيم غير موجود');
    }
}

// إضافة اختصار لوحة المفاتيح للاختبار
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'T') {
        testDarknessSystem();
    }
});

// تشغيل الإصلاح عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(fixDarknessSystem, 2000);
    });
} else {
    setTimeout(fixDarknessSystem, 2000);
}

console.log('✅ تم تحميل ملف إصلاح التعتيم - اضغط Ctrl+Shift+T للاختبار');
