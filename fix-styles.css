/* ملف إصلاح الأنماط */

/* إصلاح الساعة الرقمية */
.digital-clock {
    color: #40E0D0 !important;
    font-size: 2.2em !important;
    text-align: center !important;
    margin: 10px auto !important;
    font-weight: bold !important;
    background-color: rgba(0, 0, 0, 0.8) !important;
    padding: 15px 25px !important;
    border-radius: 15px !important;
    border: 2px solid #40E0D0 !important;
    box-shadow: 0 0 20px rgba(64, 224, 208, 0.4) !important;
    width: 85% !important;
    max-width: 280px !important;
    font-family: 'Arial', sans-serif !important;
    letter-spacing: 2px !important;
    min-height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* إصلاح الساعة التناظرية */
.analog-clock {
    width: 140px !important;
    height: 140px !important;
    border: 4px solid var(--gold-color) !important;
    border-radius: 50% !important;
    position: relative !important;
    background: radial-gradient(circle, #2c1810 0%, #1a0f08 100%) !important;
    margin: 5px 0 !important;
}

.analog-clock .number {
    position: absolute !important;
    font-size: 14px !important;
    font-weight: bold !important;
    color: var(--gold-color) !important;
    transform: translate(-50%, -50%) !important;
}

/* مواضع الأرقام */
.analog-clock .number:nth-child(1) { top: 10px !important; left: 50% !important; } /* 12 */
.analog-clock .number:nth-child(2) { top: 25px !important; right: 15px !important; } /* 1 */
.analog-clock .number:nth-child(3) { top: 45px !important; right: 8px !important; } /* 2 */
.analog-clock .number:nth-child(4) { top: 50% !important; right: 5px !important; } /* 3 */
.analog-clock .number:nth-child(5) { bottom: 45px !important; right: 8px !important; } /* 4 */
.analog-clock .number:nth-child(6) { bottom: 25px !important; right: 15px !important; } /* 5 */
.analog-clock .number:nth-child(7) { bottom: 10px !important; left: 50% !important; } /* 6 */
.analog-clock .number:nth-child(8) { bottom: 25px !important; left: 15px !important; } /* 7 */
.analog-clock .number:nth-child(9) { bottom: 45px !important; left: 8px !important; } /* 8 */
.analog-clock .number:nth-child(10) { top: 50% !important; left: 5px !important; } /* 9 */
.analog-clock .number:nth-child(11) { top: 45px !important; left: 8px !important; } /* 10 */
.analog-clock .number:nth-child(12) { top: 25px !important; left: 15px !important; } /* 11 */

/* إصلاح عقارب الساعة */
.hand {
    position: absolute !important;
    background: var(--gold-color) !important;
    transform-origin: bottom center !important;
    border-radius: 2px !important;
}

.hour-hand {
    width: 4px !important;
    height: 35% !important;
    bottom: 50% !important;
    left: 50% !important;
    background: var(--hour-hand-color, var(--gold-color)) !important;
}

.minute-hand {
    width: 3px !important;
    height: 40% !important;
    bottom: 50% !important;
    left: 50% !important;
    background: var(--minute-hand-color, var(--gold-color)) !important;
}

.second-hand {
    width: 1px !important;
    height: 45% !important;
    bottom: 50% !important;
    left: 50% !important;
    background: var(--second-hand-color, #ff0000) !important;
}

.center-dot {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 8px !important;
    height: 8px !important;
    background: var(--gold-color) !important;
    border-radius: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 10 !important;
}

/* إصلاح مواقيت الصلاة */
.prayer-times {
    background-color: rgba(74, 59, 59, 0.95) !important;
    color: #71d3ee !important;
    padding: 15px !important;
    border-radius: 10px !important;
    margin: 10px 0 !important;
    border: 2px solid #71d3ee !important;
    box-shadow: 0 0 15px rgba(113, 211, 238, 0.3) !important;
}

.prayer-times .prayer-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 8px 0 !important;
    border-bottom: 1px solid rgba(113, 211, 238, 0.2) !important;
}

.prayer-times .prayer-item:last-child {
    border-bottom: none !important;
}

.prayer-times .prayer-name {
    font-weight: bold !important;
    color: #71d3ee !important;
    font-size: 1.1em !important;
}

.prayer-times .prayer-time {
    color: #40E0D0 !important;
    font-weight: bold !important;
    font-size: 1.2em !important;
    font-family: 'Arial', sans-serif !important;
}

/* إصلاح الخلفيات */
body {
    background-attachment: fixed !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}

/* إصلاح الأزرار */
.save-settings-btn, .settings-button {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
    color: white !important;
    border: none !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: bold !important;
    margin: 5px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

.save-settings-btn:hover, .settings-button:hover {
    background: linear-gradient(135deg, #45a049, #4CAF50) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 12px rgba(0,0,0,0.3) !important;
}

/* إصلاح حقول الإدخال */
.time-input, .offset-input, input[type="time"], input[type="number"] {
    background: rgba(255, 255, 255, 0.9) !important;
    border: 2px solid #71d3ee !important;
    border-radius: 5px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    color: #333 !important;
    margin: 5px !important;
    transition: all 0.3s ease !important;
}

.time-input:focus, .offset-input:focus, input[type="time"]:focus, input[type="number"]:focus {
    border-color: #40E0D0 !important;
    box-shadow: 0 0 10px rgba(64, 224, 208, 0.3) !important;
    outline: none !important;
}

/* إصلاح القوائم المنسدلة */
select {
    background: rgba(255, 255, 255, 0.9) !important;
    border: 2px solid #71d3ee !important;
    border-radius: 5px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    color: #333 !important;
    margin: 5px !important;
    cursor: pointer !important;
}

select:focus {
    border-color: #40E0D0 !important;
    box-shadow: 0 0 10px rgba(64, 224, 208, 0.3) !important;
    outline: none !important;
}

/* إصلاح الإشعارات */
.notification {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    padding: 15px 20px !important;
    border-radius: 8px !important;
    color: white !important;
    font-weight: bold !important;
    z-index: 10000 !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
    animation: slideIn 0.3s ease !important;
}

.notification.success {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
}

.notification.error {
    background: linear-gradient(135deg, #f44336, #d32f2f) !important;
}

.notification.info {
    background: linear-gradient(135deg, #2196F3, #1976D2) !important;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* إصلاح التجاوب */
@media (max-width: 768px) {
    .digital-clock {
        font-size: 1.8em !important;
        padding: 12px 20px !important;
        max-width: 90% !important;
    }
    
    .analog-clock {
        width: 120px !important;
        height: 120px !important;
    }
    
    .prayer-times {
        padding: 10px !important;
        margin: 5px !important;
    }
}
