// إنشاء ملفات صوتية بديلة
console.log('🔊 إنشاء ملفات صوتية بديلة...');

// دالة لإنشاء صوت بديل باستخدام Web Audio API
function createSilentAudio(duration = 5) {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const sampleRate = audioContext.sampleRate;
    const frameCount = sampleRate * duration;
    
    const audioBuffer = audioContext.createBuffer(1, frameCount, sampleRate);
    const channelData = audioBuffer.getChannelData(0);
    
    // إنشاء نغمة بسيطة بدلاً من الصمت
    for (let i = 0; i < frameCount; i++) {
        // نغمة بسيطة 440 Hz (نوتة A)
        channelData[i] = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 0.1;
    }
    
    return audioBuffer;
}

// دالة لتحويل AudioBuffer إلى Blob
function audioBufferToBlob(audioBuffer) {
    const length = audioBuffer.length;
    const arrayBuffer = new ArrayBuffer(length * 2);
    const view = new DataView(arrayBuffer);
    
    // تحويل البيانات الصوتية إلى 16-bit PCM
    const channelData = audioBuffer.getChannelData(0);
    for (let i = 0; i < length; i++) {
        const sample = Math.max(-1, Math.min(1, channelData[i]));
        view.setInt16(i * 2, sample * 0x7FFF, true);
    }
    
    return new Blob([arrayBuffer], { type: 'audio/wav' });
}

// دالة لإنشاء ملف صوتي بديل
function createFallbackAudio() {
    try {
        const audioBuffer = createSilentAudio(10); // 10 ثواني
        const blob = audioBufferToBlob(audioBuffer);
        const url = URL.createObjectURL(blob);
        
        // إنشاء عنصر صوتي مؤقت
        const audio = new Audio(url);
        audio.preload = 'auto';
        
        // حفظ الرابط للاستخدام لاحقاً
        window.fallbackAdhanUrl = url;
        
        console.log('✅ تم إنشاء ملف صوتي بديل');
        return url;
    } catch (error) {
        console.error('خطأ في إنشاء الملف الصوتي البديل:', error);
        return null;
    }
}

// دالة لإصلاح مشكلة الأذان
function fixAdhanAudio() {
    console.log('🔧 إصلاح مشكلة صوت الأذان...');
    
    // البحث عن عنصر الأذان
    let adhanAudio = document.getElementById('adhan-audio');
    if (!adhanAudio) {
        // إنشاء عنصر صوتي جديد
        adhanAudio = document.createElement('audio');
        adhanAudio.id = 'adhan-audio';
        adhanAudio.preload = 'auto';
        document.body.appendChild(adhanAudio);
        console.log('✅ تم إنشاء عنصر صوتي جديد');
    }
    
    // قائمة بمسارات الأذان البديلة
    const adhanPaths = [
        'audio/normal_adhan.mp3',
        'audio/adhan.mp3',
        'audio/azan.mp3',
        'sounds/adhan.mp3',
        'sounds/azan.mp3'
    ];
    
    // محاولة تحميل ملف صوتي
    let audioLoaded = false;
    
    function tryLoadAudio(index = 0) {
        if (index >= adhanPaths.length) {
            // إذا فشل تحميل جميع الملفات، استخدم الصوت البديل
            console.log('⚠️ فشل تحميل جميع ملفات الأذان، استخدام صوت بديل...');
            const fallbackUrl = createFallbackAudio();
            if (fallbackUrl) {
                adhanAudio.src = fallbackUrl;
                console.log('✅ تم تعيين صوت بديل');
            }
            return;
        }
        
        const testAudio = new Audio();
        testAudio.addEventListener('canplaythrough', function() {
            adhanAudio.src = adhanPaths[index];
            audioLoaded = true;
            console.log(`✅ تم تحميل ملف الأذان: ${adhanPaths[index]}`);
        });
        
        testAudio.addEventListener('error', function() {
            console.log(`❌ فشل تحميل: ${adhanPaths[index]}`);
            tryLoadAudio(index + 1);
        });
        
        testAudio.src = adhanPaths[index];
    }
    
    // بدء محاولة التحميل
    tryLoadAudio();
    
    // إصلاح دالة تشغيل الأذان
    window.playAdhanFixed = function() {
        return new Promise((resolve, reject) => {
            if (!adhanAudio.src) {
                console.log('⚠️ لا يوجد ملف صوتي، تشغيل صوت بديل...');
                
                // تشغيل نغمة بديلة باستخدام Web Audio API
                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);
                    
                    oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                    
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 3);
                    
                    setTimeout(resolve, 3000);
                    console.log('✅ تم تشغيل نغمة بديلة');
                } catch (error) {
                    console.error('خطأ في تشغيل النغمة البديلة:', error);
                    resolve(); // حل الوعد حتى لو فشل
                }
                return;
            }
            
            adhanAudio.currentTime = 0;
            const playPromise = adhanAudio.play();
            
            if (playPromise !== undefined) {
                playPromise
                    .then(() => {
                        console.log('✅ تم تشغيل الأذان بنجاح');
                        resolve();
                    })
                    .catch(error => {
                        console.error('خطأ في تشغيل الأذان:', error);
                        reject(error);
                    });
            } else {
                resolve();
            }
        });
    };
    
    console.log('✅ تم إصلاح نظام صوت الأذان');
}

// دالة لإصلاح مشكلة الإقامة
function fixIqamahAudio() {
    console.log('🔧 إصلاح مشكلة صوت الإقامة...');
    
    // البحث عن عنصر الإقامة أو إنشاؤه
    let iqamahAudio = document.getElementById('iqamah-audio');
    if (!iqamahAudio) {
        iqamahAudio = document.createElement('audio');
        iqamahAudio.id = 'iqamah-audio';
        iqamahAudio.preload = 'auto';
        document.body.appendChild(iqamahAudio);
        console.log('✅ تم إنشاء عنصر صوت الإقامة');
    }
    
    // إصلاح دالة تشغيل الإقامة
    window.playIqamahFixed = function() {
        return new Promise((resolve) => {
            console.log('🔊 تشغيل صوت الإقامة البديل...');
            
            // تشغيل نغمة مختلفة للإقامة
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                // نغمة مختلفة للإقامة (523 Hz - نوتة C)
                oscillator.frequency.setValueAtTime(523, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                
                oscillator.start();
                oscillator.stop(audioContext.currentTime + 2);
                
                setTimeout(resolve, 2000);
                console.log('✅ تم تشغيل نغمة الإقامة البديلة');
            } catch (error) {
                console.error('خطأ في تشغيل نغمة الإقامة:', error);
                resolve();
            }
        });
    };
    
    console.log('✅ تم إصلاح نظام صوت الإقامة');
}

// تشغيل الإصلاحات
function runAudioFixes() {
    console.log('🚀 بدء إصلاح المشاكل الصوتية...');
    
    fixAdhanAudio();
    fixIqamahAudio();
    
    console.log('✅ تم إصلاح جميع المشاكل الصوتية');
}

// تشغيل الإصلاحات عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(runAudioFixes, 2000);
    });
} else {
    setTimeout(runAudioFixes, 2000);
}

console.log('✅ تم تحميل إصلاح المشاكل الصوتية');
